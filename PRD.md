# bidtrakr - Driver Performance Tracker

## Table of Contents

1. [Application Overview](#application-overview)
2. [Architecture](#architecture)
3. [Database Design](#database-design)
4. [Features](#features)
5. [User Workflows](#user-workflows)
6. [UI/UX Design](#uiux-design)
    - [Design System Overview](#design-system-overview)
    - [Screen-Specific Layouts](#screen-specific-layouts)
    - [Income Screen](#uiux-details-income-screen)
    - [Income Details Screen](#uiux-details-income-details-screen)
    - [Orders Screen](#uiux-details-orders-screen)
    - [Order Details Screen](#uiux-details-order-details-screen)
    - [Performance Screen](#uiux-details-performance-screen)
    - [Performance Details Screen](#uiux-details-performance-details-screen)
    - [Spare Parts Screens](#uiux-details-spare-parts-screens)
    - [Spare Parts Options Bottom Sheet](#uiux-details-spare-parts-options-bottom-sheet)
    - [Backup Screens](#uiux-details-backup-screens)
    - [Backup Actions Bottom Sheet](#uiux-details-backup-actions-bottom-sheet)
    - [Restore Backup Confirmation Dialog](#uiux-details-restore-backup-confirmation-dialog)
    - [Settings Screen](#uiux-details-settings-screen)
    - [Levels Screens](#uiux-details-levels-screens)
    - [Authentication Screens](#uiux-details-authentication-screens)
    - [Cloud Sync Screen](#uiux-details-cloud-sync-screen)
    - [More Screen](#uiux-details-more-screen)
    - [About App Dialog](#uiux-details-about-app-dialog)
7. [Bottom Navigation System](#bottom-navigation-system)
8. [Technical Implementation](#technical-implementation)
9. [Sync & Cloud Services](#sync--cloud-services)
10. [Authentication & Security](#authentication--security)
11. [Backup & Data Management](#backup--data-management)
12. [Development & Deployment](#development--deployment)
13. [Application Initialization & Configuration](#application-initialization--configuration)
14. [Testing Strategy](#testing-strategy)
15. [Error Handling & Logging](#error-handling--logging)
16. [Performance Optimization](#performance-optimization)
17. [Security Considerations](#security-considerations)
18. [Troubleshooting Guide](#troubleshooting-guide)
19. [Code Quality & Standards](#code-quality--standards)
20. [Future Roadmap](#future-roadmap)
21. [Conclusion](#conclusion)

---

## Application Overview

**bidtrakr** is a comprehensive Flutter application designed for driver performance tracking and income management. The app helps drivers monitor their daily earnings, order performance, vehicle maintenance, and overall productivity metrics.

### Key Features
- **Income Tracking**: Monitor daily earnings across multiple payment methods
- **Order Management**: Track completed, missed, and canceled orders
- **Performance Analytics**: Analyze bid acceptance rates and trip completion metrics
- **Spare Parts Management**: Monitor vehicle maintenance and replacement schedules
- **Level System**: Gamified progression system with platinum, gold, and silver tiers
- **Cloud Sync**: Secure data synchronization with Supabase backend
- **Backup & Restore**: Local data backup and restoration capabilities

---

## Architecture

### Clean Architecture Pattern
The application follows Clean Architecture principles with clear separation of concerns:

```
lib/
├── core/                    # Core application layer
│   ├── components/         # Reusable UI components
│   ├── config/            # Configuration files
│   ├── constants/         # App constants
│   ├── datasources/       # Database layer
│   ├── di/               # Dependency injection
│   ├── errors/           # Error handling
│   ├── initialization/   # App initialization
│   ├── models/          # Data models
│   ├── providers/       # State management
│   ├── repositories/    # Data repositories
│   ├── services/        # Business logic services
│   ├── theme/          # UI theming
│   ├── utils/          # Utility functions
│   ├── version/        # Version management
│   └── widgets/        # Shared widgets
└── features/           # Feature modules
    ├── auth/           # Authentication
    ├── backup/         # Backup & restore
    ├── home/           # Main navigation
    ├── income/         # Income tracking
    ├── levels/         # Level system
    ├── more/           # Additional features
    ├── orders/         # Order management
    ├── performance/    # Performance analytics
    ├── settings/       # App settings
    ├── spare_parts/    # Spare parts management
    └── sync/           # Sync functionality
```

### State Management
- **Riverpod**: Primary state management solution
- **Provider Pattern**: For dependency injection and state sharing
- **AsyncValue**: For handling loading, error, and success states

---

## Database Design

### Core Tables

#### 1. Income Table
```sql
CREATE TABLE income (
    uuid TEXT PRIMARY KEY,
    id INTEGER AUTOINCREMENT,
    date DATETIME NOT NULL,
    initial_mileage INTEGER NOT NULL,
    final_mileage INTEGER NOT NULL,
    initial_gopay REAL NOT NULL,
    initial_bca REAL NOT NULL,
    initial_cash REAL NOT NULL,
    initial_ovo REAL NOT NULL,
    initial_bri REAL NOT NULL,
    initial_rekpon REAL NOT NULL,
    final_gopay REAL NOT NULL,
    final_bca REAL NOT NULL,
    final_cash REAL NOT NULL,
    final_ovo REAL NOT NULL,
    final_bri REAL NOT NULL,
    final_rekpon REAL NOT NULL,
    initial_capital REAL,
    final_result REAL,
    mileage INTEGER,
    net_income REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME,
    sync_status TEXT DEFAULT 'pendingUpload'
);
```

#### 2. Orders Table
```sql
CREATE TABLE orders (
    uuid TEXT PRIMARY KEY,
    id INTEGER AUTOINCREMENT,
    date DATETIME NOT NULL,
    order_completed INTEGER NOT NULL,
    order_missed INTEGER NOT NULL,
    order_canceled INTEGER NOT NULL,
    cbs_order INTEGER NOT NULL,
    incoming_order INTEGER,
    order_received INTEGER,
    bid_acceptance REAL,
    trip_completion REAL,
    points INTEGER NOT NULL,
    trip REAL NOT NULL,
    bonus REAL NOT NULL,
    tips REAL NOT NULL,
    income REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME,
    sync_status TEXT DEFAULT 'pendingUpload'
);
```

#### 3. Performance Table
```sql
CREATE TABLE performance (
    uuid TEXT PRIMARY KEY,
    id INTEGER AUTOINCREMENT,
    date DATETIME NOT NULL,
    bid_performance REAL NOT NULL,
    trip_performance REAL NOT NULL,
    active_days INTEGER NOT NULL,
    online_hours REAL NOT NULL,
    avg_completed REAL,
    avg_online REAL,
    retention REAL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME,
    sync_status TEXT DEFAULT 'pendingUpload'
);
```

#### 4. Spare Parts Table
```sql
CREATE TABLE spare_parts (
    uuid TEXT PRIMARY KEY,
    id INTEGER AUTOINCREMENT,
    part_name TEXT NOT NULL,
    part_type TEXT NOT NULL,
    price REAL NOT NULL,
    mileage_limit INTEGER NOT NULL,
    initial_mileage INTEGER NOT NULL,
    installation_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    current_mileage INTEGER DEFAULT 0,
    warning_status BOOLEAN DEFAULT FALSE,
    replacement_count INTEGER DEFAULT 0,
    notes TEXT DEFAULT '',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME,
    sync_status TEXT DEFAULT 'pendingUpload'
);
```

#### 5. Spare Parts History Table
```sql
CREATE TABLE spare_parts_history (
    uuid TEXT PRIMARY KEY,
    id INTEGER AUTOINCREMENT,
    part_name TEXT NOT NULL,
    part_type TEXT NOT NULL,
    price REAL NOT NULL,
    replacement_date DATETIME NOT NULL,
    mileage_at_replacement INTEGER NOT NULL,
    spare_part_id INTEGER REFERENCES spare_parts(id),
    installation_date DATETIME NOT NULL,
    initial_mileage INTEGER NOT NULL,
    replacement_reason TEXT DEFAULT 'Regular maintenance',
    replaced_by_part_id INTEGER,
    replacement_count INTEGER DEFAULT 1,
    usage_days INTEGER DEFAULT 0,
    usage_mileage INTEGER DEFAULT 0,
    notes TEXT DEFAULT '',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME,
    sync_status TEXT DEFAULT 'pendingUpload'
);
```

#### 6. Level Settings Table
```sql
CREATE TABLE level_settings (
    id INTEGER AUTOINCREMENT PRIMARY KEY,
    platinum_points_req INTEGER NOT NULL,
    platinum_bid_req REAL NOT NULL,
    platinum_trip_req REAL NOT NULL,
    gold_points_req INTEGER NOT NULL,
    gold_bid_req REAL NOT NULL,
    gold_trip_req REAL NOT NULL,
    silver_points_req INTEGER NOT NULL,
    silver_bid_req REAL NOT NULL,
    silver_trip_req REAL NOT NULL
);
```

#### 7. App Settings Table
```sql
CREATE TABLE app_settings (
    id INTEGER AUTOINCREMENT PRIMARY KEY,
    date_range_start DATETIME NOT NULL,
    date_range_end DATETIME NOT NULL,
    backup_directory_path TEXT,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_sync_time DATETIME
);
```

### Database Features
- **UUID Support**: All tables use UUIDs for unique identification
- **Soft Deletes**: Records are marked as deleted rather than physically removed
- **Sync Status Tracking**: Tracks synchronization status for cloud sync
- **Computed Fields**: Automatic calculation of derived values
- **Triggers**: Automatic timestamp updates and sync status management

---

## Features

### 1. Income Tracking
- **Multi-payment Method Support**: Track earnings across GoPay, BCA, Cash, OVO, BRI, and RekPon
- **Mileage Tracking**: Monitor vehicle mileage for expense calculations
- **Net Income Calculation**: Automatic calculation of net earnings
- **Date Range Filtering**: View income data for specific time periods
- **Trend Analysis**: Visual charts showing income trends over time

### 2. Order Management
- **Order Statistics**: Track completed, missed, and canceled orders
- **Performance Metrics**: Calculate bid acceptance and trip completion rates
- **Financial Tracking**: Monitor trip earnings, bonuses, and tips
- **Points System**: Track driver points for level progression
- **Historical Analysis**: View order performance over time

### 3. Performance Analytics
- **Bid Performance**: Track bid acceptance rates
- **Trip Performance**: Monitor trip completion metrics
- **Activity Tracking**: Record active days and online hours
- **Retention Analysis**: Calculate driver retention rates
- **Comparative Analysis**: Compare performance across time periods

### 4. Spare Parts Management
- **Part Inventory**: Track installed spare parts
- **Maintenance Scheduling**: Monitor replacement schedules based on mileage
- **Cost Tracking**: Record part costs and replacement expenses
- **History Tracking**: Maintain complete replacement history
- **Warning System**: Alert when parts need replacement
- **Usage Analytics**: Track part usage and lifespan

### 5. Level System
- **Tier Progression**: Bronze → Silver → Gold → Platinum
- **Requirement Tracking**: Monitor progress towards next level
- **Performance Metrics**: Points, bid rates, and trip completion requirements
- **Visual Progress**: Progress bars and achievement indicators
- **Settings Management**: Configurable level requirements

### 6. Cloud Synchronization
- **Automatic Sync**: Background synchronization with cloud
- **Conflict Resolution**: Handle data conflicts between local and cloud
- **Offline Support**: Work offline with local database
- **Sync Status Tracking**: Monitor sync progress and status
- **Manual Sync**: Trigger sync operations manually

### 7. Backup & Restore
- **Local Backup**: Export database to local storage
- **Restore Functionality**: Import data from backup files
- **Backup Scheduling**: Automatic backup creation
- **Data Validation**: Verify backup integrity
- **Multiple Formats**: Support for various backup formats

---

## User Workflows

### 1. Income Tracking Workflow

#### Daily Income Entry Workflow
1. **Open Income Screen**
   - User taps the "Income" tab in bottom navigation
   - App displays income summary and recent entries

2. **Add New Income Record**
   - User taps the floating action button (+)
   - App opens income form screen

3. **Enter Income Data**
   - **Date Selection**: User selects the date for the income record
   - **Mileage Entry**: User enters initial and final mileage
   - **Payment Method Entries**:
     - GoPay: Enter initial and final amounts
     - BCA: Enter initial and final amounts
     - Cash: Enter initial and final amounts
     - OVO: Enter initial and final amounts
     - BRI: Enter initial and final amounts
     - RekPon: Enter initial and final amounts

4. **Review and Save**
   - App automatically calculates:
     - Net income for each payment method
     - Total mileage driven
     - Overall net income
   - User reviews calculated values
   - User taps "Save" to store the record

5. **Confirmation**
   - App shows success message
   - User returns to income list
   - New record appears in the list

#### Income Review and Analysis Workflow
1. **Filter Income Data**
   - User taps date range selector
   - User selects custom date range or predefined periods
   - App filters and displays relevant income records

2. **View Income Summary**
   - App displays summary cards showing:
     - Total income for selected period
     - Average daily income
     - Total mileage
     - Income trends

3. **Analyze Trends**
   - User taps on trend charts
   - App shows detailed income breakdown
   - User can view income by payment method

4. **Export or Share**
   - User taps share button
   - App generates income report
   - User can share via email or messaging apps

### 2. Order Management Workflow

#### Daily Order Entry Workflow
1. **Open Orders Screen**
   - User taps the "Orders" tab in bottom navigation
   - App displays order summary and recent entries

2. **Add New Order Record**
   - User taps the floating action button (+)
   - App opens order form screen

3. **Enter Order Statistics**
   - **Date Selection**: User selects the date for the order record
   - **Order Counts**:
     - Completed orders: Number of successfully completed orders
     - Missed orders: Number of orders that were missed
     - Canceled orders: Number of canceled orders
     - CBS orders: Number of CBS (Call Back System) orders

4. **Enter Financial Data**
   - **Trip Earnings**: Amount earned from trips
   - **Bonus**: Additional bonus payments
   - **Tips**: Tips received from customers
   - **Points**: Points earned for the day

5. **Review and Save**
   - App automatically calculates:
     - Incoming orders (completed + missed + canceled)
     - Order received percentage
     - Bid acceptance rate
     - Trip completion rate
     - Total income for the day
   - User reviews calculated values
   - User taps "Save" to store the record

#### Order Performance Analysis Workflow
1. **View Order Metrics**
   - App displays performance cards showing:
     - Order completion rate
     - Bid acceptance rate
     - Average earnings per order
     - Total points earned

2. **Analyze Trends**
   - User taps on performance charts
   - App shows order trends over time
   - User can compare performance across different periods

3. **Export Performance Data**
   - User taps export button
   - App generates performance report
   - User can share or save the report

### 3. Performance Analytics Workflow

#### Performance Data Entry Workflow
1. **Open Performance Screen**
   - User taps the "Performance" tab in bottom navigation
   - App displays performance summary and recent entries

2. **Add New Performance Record**
   - User taps the floating action button (+)
   - App opens performance form screen

3. **Enter Performance Metrics**
   - **Date Selection**: User selects the date for the performance record
   - **Bid Performance**: Percentage of bids accepted
   - **Trip Performance**: Percentage of trips completed
   - **Active Days**: Number of days actively working
   - **Online Hours**: Total hours spent online

4. **Review and Save**
   - App automatically calculates:
     - Average completed orders per day
     - Average online hours per day
     - Retention rate
   - User reviews calculated values
   - User taps "Save" to store the record

#### Performance Analysis Workflow
1. **View Performance Dashboard**
   - App displays key performance indicators
   - User can see trends and patterns

2. **Filter Performance Data**
   - User selects date range for analysis
   - App updates charts and metrics accordingly

3. **Export Performance Reports**
   - User taps export button
   - App generates comprehensive performance report
   - User can share or save the report

### 4. Spare Parts Management Workflow

#### Adding New Spare Part Workflow
1. **Open Spare Parts Screen**
   - User taps "More" tab → "Spare Parts"
   - App displays list of installed spare parts

2. **Add New Spare Part**
   - User taps the floating action button (+)
   - App opens spare part form screen

3. **Enter Part Information**
   - **Part Name**: Name of the spare part
   - **Part Type**: Category of the part (e.g., Engine, Brakes, Tires)
   - **Price**: Cost of the part
   - **Mileage Limit**: Recommended replacement mileage
   - **Initial Mileage**: Current vehicle mileage when installing
   - **Installation Date**: Date when part was installed
   - **Notes**: Additional information about the part

4. **Save Part Record**
   - User reviews entered information
   - User taps "Save" to store the part record
   - App adds the part to the inventory

#### Spare Part Monitoring Workflow
1. **View Spare Parts List**
   - App displays all installed spare parts
   - Each part shows:
     - Current usage percentage
     - Days in use
     - Warning status (if applicable)

2. **Check Part Status**
   - User taps on a specific part
   - App shows detailed part information
   - User can see usage statistics and replacement history

3. **Replace Spare Part Workflow**
   - User taps "Replace" button on part card
   - App opens replacement form
   - User enters:
     - Replacement date
     - Current mileage
     - Replacement reason
     - Notes
   - User taps "Confirm Replacement"
   - App moves old part to history and creates new part record

#### Maintenance Alerts Workflow
1. **Receive Warning Notifications**
   - App shows warning indicators on parts nearing replacement
   - User receives push notifications (if enabled)

2. **Review Maintenance Schedule**
   - User taps on warning indicators
   - App shows detailed maintenance recommendations
   - User can schedule replacement or dismiss warnings

### 5. Replacement History Workflow

#### Accessing Replacement History Workflow
1. **Open Spare Parts Screen**
   - User taps "More" tab → "Spare Parts"
   - App displays list of installed spare parts

2. **Select Spare Part**
   - User taps on a specific spare part card
   - App shows bottom sheet with options

3. **View History**
   - User taps "View Replacement History" option
   - App navigates to replacement history screen

#### Replacement History Screen Features

**History Display:**
- **Chronological List**: All replacements displayed in reverse chronological order
- **Replacement Cards**: Each replacement shown as an expandable card
- **Visual Indicators**: Most recent replacement highlighted with primary color
- **Empty State**: Special message when no history exists

**History Card Information:**
- **Replacement Number**: Sequential numbering (Replacement #1, #2, etc.)
- **Replacement Date**: When the part was replaced
- **Mileage at Replacement**: Vehicle mileage when replacement occurred
- **Part Price**: Cost of the replaced part
- **Usage Statistics**: Days in use and distance traveled
- **Replacement Reason**: Why the part was replaced
- **Additional Notes**: Any extra information about the replacement

#### History Card Details Workflow
1. **Expand Card Information**
   - User taps on "Replacement Reason" section
   - App expands to show detailed reason and notes
   - User can read full replacement context

2. **View Additional Information**
   - User taps on "Additional Information" section
   - App shows:
     - Replacement count number
     - Record creation timestamp
     - Technical details

3. **Refresh History**
   - User pulls down to refresh the history list
   - App reloads latest replacement data
   - User sees updated history immediately

#### Replacement History Data Structure

**Core History Fields:**
```dart
class EnhancedReplacementHistory {
  final int? id;
  final String partName;
  final String partType;
  final double price;
  final DateTime replacementDate;
  final int mileageAtReplacement;
  final int sparePartId;
  final DateTime installationDate;
  final int initialMileage;
  final String replacementReason;
  final int? replacedByPartId;
  final int replacementCount;
  final String notes;
  final DateTime createdAt;
  
  // Computed fields
  final int usageDays;
  final int usageMileage;
}
```

**Computed Values:**
- **Usage Days**: `replacementDate.difference(installationDate).inDays`
- **Usage Mileage**: `mileageAtReplacement - initialMileage`

#### History Creation Process

**During Replacement:**
1. **Save Old Part History**
   ```dart
   // Create history entry for the replaced part
   final historyResult = await saveReplacementHistory(
     partName: oldPart.partName,
     partType: oldPart.partType,
     price: oldPart.price,
     replacementDate: replacementDate,
     mileageAtReplacement: currentMileage,
     sparePartId: oldPart.id!,
     installationDate: oldPart.installationDate,
     initialMileage: oldPart.initialMileage,
     replacementReason: replacementReason,
     notes: notes,
   );
   ```

2. **Create New Part Record**
   ```dart
   // Create new spare part with current mileage as initial
   final updatedPart = EnhancedSparePart.withComputedValues(
     id: oldPart.id,
     partName: newPartName ?? oldPart.partName,
     partType: newPartType ?? oldPart.partType,
     price: newPartPrice,
     mileageLimit: newMileageLimit ?? oldPart.mileageLimit,
     initialMileage: currentMileage, // Current mileage becomes new initial
     installationDate: replacementDate,
     currentMileage: currentMileage,
     warningStatus: false,
     replacementCount: oldPart.replacementCount + 1,
     notes: notes,
   );
   ```

#### History Retrieval Process

**Database Query:**
```dart
Future<Either<Failure, List<EnhancedReplacementHistory>>> getReplacementHistoryForPart(int sparePartId) async {
  try {
    // Get all history entries for this part
    final historyList = await database.getHistoryForSparePart(sparePartId);
    
    // Sort by date, oldest first (for processing)
    historyList.sort((a, b) => a.replacementDate.compareTo(b.replacementDate));
    
    // Create enhanced history entries with computed values
    final enhancedHistoryList = <EnhancedReplacementHistory>[];
    
    for (final currentHistory in historyList) {
      final enhancedHistory = EnhancedReplacementHistory.withComputedValues(
        id: currentHistory.id,
        partName: currentHistory.partName,
        partType: currentHistory.partType,
        price: currentHistory.price,
        replacementDate: currentHistory.replacementDate,
        mileageAtReplacement: currentHistory.mileageAtReplacement,
        sparePartId: currentHistory.sparePartId,
        installationDate: currentHistory.installationDate,
        initialMileage: currentHistory.initialMileage,
        replacementReason: currentHistory.replacementReason,
        replacedByPartId: currentHistory.replacedByPartId,
        replacementCount: currentHistory.replacementCount,
        notes: currentHistory.notes,
        createdAt: currentHistory.createdAt,
      );
      
      enhancedHistoryList.add(enhancedHistory);
    }
    
    // Sort by date, most recent first (for display)
    enhancedHistoryList.sort((a, b) => b.replacementDate.compareTo(a.replacementDate));
    
    return Right(enhancedHistoryList);
  } catch (e) {
    return Left(Failure.database(e.toString()));
  }
}
```

#### History UI Components

**History List Widget:**
- **RefreshIndicator**: Pull-to-refresh functionality
- **ListView.builder**: Efficient list rendering
- **HistoryCardWidget**: Individual history entry display

**History Card Widget:**
- **Expandable Sections**: Replacement reason and additional information
- **Visual Hierarchy**: Clear information organization
- **Color Coding**: Different colors for different replacement numbers

**Empty State Widget:**
- **Current Part Statistics**: Shows current part information
- **Helpful Message**: Explains when history will appear
- **Refresh Capability**: Allows manual refresh

#### Error Handling

**Error States:**
1. **Loading Error**: Network or database connection issues
2. **Empty History**: No replacement records exist
3. **Data Corruption**: Invalid history data

**Error Recovery:**
- **Retry Button**: Manual refresh option
- **Error Messages**: Clear explanation of issues
- **Graceful Degradation**: App continues to function

#### History Data Persistence

**Database Schema:**
```sql
CREATE TABLE spare_parts_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  part_name TEXT NOT NULL,
  part_type TEXT NOT NULL,
  price REAL NOT NULL,
  replacement_date TEXT NOT NULL,
  mileage_at_replacement INTEGER NOT NULL,
  spare_part_id INTEGER NOT NULL,
  installation_date TEXT NOT NULL,
  initial_mileage INTEGER NOT NULL,
  replacement_reason TEXT DEFAULT 'Regular maintenance',
  replaced_by_part_id INTEGER,
  replacement_count INTEGER DEFAULT 1,
  usage_days INTEGER,
  usage_mileage INTEGER,
  notes TEXT,
  created_at TEXT NOT NULL,
  FOREIGN KEY (spare_part_id) REFERENCES spare_parts(id)
);
```

**Data Integrity:**
- **Foreign Key Constraints**: Ensures data consistency
- **Required Fields**: Critical information always present
- **Default Values**: Sensible defaults for optional fields
- **Timestamp Tracking**: Creation time for audit trail

### 6. Level System Workflow

#### Level Progress Tracking Workflow
1. **Open Level Screen**
   - User taps "More" tab → "Level"
   - App displays current level and progress

2. **View Current Level**
   - App shows:
     - Current level (Bronze/Silver/Gold/Platinum)
     - Progress towards next level
     - Current performance metrics
     - Requirements for next level

3. **Check Level Requirements**
   - User taps on level requirements
   - App shows detailed breakdown of:
     - Points needed
     - Bid acceptance rate required
     - Trip completion rate required

4. **Monitor Progress**
   - App automatically updates progress as user performs activities
   - User can see real-time progress towards next level

#### Level Settings Management Workflow
1. **Access Level Settings**
   - User taps settings icon in level screen
   - App opens level settings screen

2. **Configure Level Requirements**
   - User can adjust requirements for each level
   - User sets points, bid rates, and trip completion requirements
   - User taps "Save" to update settings

### 6. Cloud Synchronization Workflow

#### Initial Setup Workflow
1. **Enable Sync**
   - User taps "More" tab → "Cloud Sync"
   - User toggles sync to "Enabled"

2. **Login to Account**
   - User taps "Login" button
   - User enters email and password
   - App authenticates with Supabase

3. **First Sync**
   - App automatically performs initial sync
   - App uploads local data to cloud
   - App downloads any existing cloud data

#### Daily Sync Workflow
1. **Automatic Background Sync**
   - App automatically syncs in background
   - User sees sync status indicator in app bar

2. **Manual Sync**
   - User can tap "Sync Now" button
   - App shows sync progress
   - User receives confirmation when sync completes

3. **Conflict Resolution**
   - If conflicts occur, app shows conflict resolution dialog
   - User chooses which version to keep
   - App resolves conflicts and continues sync

#### Offline Workflow
1. **Work Offline**
   - App continues to function without internet
   - All data is stored locally
   - User can perform all normal operations

2. **Queue Changes**
   - App queues changes for later sync
   - User sees pending sync indicator

3. **Sync When Online**
   - When internet connection is restored
   - App automatically syncs queued changes
   - User receives sync completion notification

### 7. Backup & Restore Workflow

#### Creating Backup Workflow
1. **Access Backup Screen**
   - User taps "More" tab → "Backup & Restore"
   - App opens backup screen

2. **Create Backup**
   - User taps "Create Backup" button
   - App shows backup progress
   - App saves database to local storage
   - User receives backup completion notification

3. **Manage Backups**
   - User can view list of available backups
   - User can delete old backups
   - User can share backup files

#### Restore Data Workflow
1. **Select Backup File**
   - User taps "Restore" tab
   - User selects backup file from storage
   - App validates backup file integrity

2. **Confirm Restore**
   - App shows backup details
   - User confirms restore operation
   - App shows restore progress

3. **Complete Restore**
   - App replaces current data with backup data
   - User receives restore completion notification
   - App restarts to apply restored data

#### Export Data Workflow
1. **Select Export Format**
   - User taps "Export" button
   - User selects export format (CSV, JSON, PDF)
   - User selects data range for export

2. **Generate Export**
   - App generates export file
   - App shows export progress
   - User receives export completion notification

3. **Share Export**
   - User can share export file via email or messaging
   - User can save export file to device storage

### 8. Authentication Workflow

#### Registration Workflow
1. **Access Registration**
   - User taps "More" tab → "Login"
   - User taps "Sign Up" link

2. **Enter Registration Details**
   - User enters email address
   - User enters password
   - User confirms password
   - User taps "Sign Up" button

3. **Email Verification**
   - App sends verification email
   - User checks email and clicks verification link
   - User returns to app

4. **Complete Registration**
   - App confirms successful registration
   - User is automatically logged in
   - User can now use sync features

#### Login Workflow
1. **Access Login**
   - User taps "More" tab → "Login"
   - App shows login form

2. **Enter Credentials**
   - User enters email address
   - User enters password
   - User taps "Sign In" button

3. **Authentication**
   - App validates credentials with Supabase
   - App stores authentication token
   - User is logged in and can use sync features

#### Logout Workflow
1. **Access Account Settings**
   - User taps "More" tab → "Login"
   - User taps "Sign Out" button

2. **Confirm Logout**
   - App shows logout confirmation dialog
   - User confirms logout

3. **Complete Logout**
   - App clears authentication token
   - App disables sync features
   - User remains logged out until next login

### 9. Settings Management Workflow

#### App Settings Workflow
1. **Access Settings**
   - User taps "More" tab → "App Settings"
   - App opens settings screen

2. **Configure Date Range**
   - User sets default date range for data views
   - User can choose predefined ranges or custom range
   - App saves date range preference

3. **Configure Backup Directory**
   - User selects backup storage location
   - User can choose internal storage or external storage
   - App saves backup directory preference

4. **Save Settings**
   - User taps "Save" button
   - App applies new settings
   - User receives confirmation

#### Sync Settings Workflow
1. **Access Sync Settings**
   - User taps "More" tab → "Cloud Sync"
   - App opens sync settings screen

2. **Configure Sync Preferences**
   - User toggles automatic sync on/off
   - User sets sync frequency
   - User configures sync notifications

3. **Test Sync Connection**
   - User taps "Test Connection" button
   - App verifies connection to Supabase
   - User receives connection status

### 10. Error Handling Workflow

#### Network Error Workflow
1. **Detect Network Error**
   - App detects network connectivity issues
   - App shows offline indicator

2. **Continue Offline**
   - App continues to function with local data
   - User can perform all normal operations
   - App queues changes for later sync

3. **Resume When Online**
   - App detects restored internet connection
   - App automatically syncs queued changes
   - User receives sync completion notification

#### Data Error Workflow
1. **Detect Data Error**
   - App detects data corruption or inconsistency
   - App shows error message to user

2. **Provide Recovery Options**
   - App offers to restore from backup
   - App offers to reset data
   - User chooses recovery option

3. **Execute Recovery**
   - App performs selected recovery action
   - App restores data integrity
   - User can continue using app normally

#### Sync Conflict Workflow
1. **Detect Sync Conflict**
   - App detects conflicting data between local and cloud
   - App shows conflict resolution dialog

2. **Resolve Conflict**
   - User chooses which version to keep
   - User can merge data if appropriate
   - App resolves conflict based on user choice

3. **Complete Sync**
   - App continues sync operation
   - App confirms successful conflict resolution
   - User receives sync completion notification

---

## Automatic Calculations

The bidtrakr application includes comprehensive automatic calculations that provide users with real-time insights and derived metrics. These calculations are performed automatically when data is entered or updated, ensuring accurate and up-to-date information.

### 1. Income Calculations

#### Net Income Calculation
```dart
// Formula: Net Income = Final Result - Initial Capital
static double calculateNetIncome({
  required double finalResult,
  required double initialCapital,
}) {
  return finalResult - initialCapital;
}
```

**Components:**
- **Initial Capital**: Sum of all initial payment method amounts
  - `initialCapital = initialGopay + initialBca + initialCash + initialOvo + initialBri + initialRekpon`
- **Final Result**: Sum of all final payment method amounts
  - `finalResult = finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon`
- **Net Income**: The actual earnings for the day

#### Mileage Calculation
```dart
// Formula: Mileage = Final Mileage - Initial Mileage
static int calculateMileage({
  required int finalMileage,
  required int initialMileage,
}) {
  return finalMileage - initialMileage;
}
```

**Usage:**
- Tracks distance traveled during the day
- Used for expense calculations and spare parts maintenance scheduling

### 2. Order Management Calculations

#### Incoming Orders Calculation
```dart
// Formula: Incoming Orders = Completed + Missed + Canceled + CBS Orders
static int calculateIncomingOrder({
  required int orderCompleted,
  required int orderMissed,
  required int orderCanceled,
  required int cbsOrder,
}) {
  return orderCompleted + orderMissed + orderCanceled + cbsOrder;
}
```

#### Order Received Calculation
```dart
// Formula: Order Received = Completed + Canceled
static int calculateOrderReceived({
  required int orderCompleted,
  required int orderCanceled,
}) {
  return orderCompleted + orderCanceled;
}
```

#### Bid Acceptance Rate Calculation
```dart
// Formula: Bid Acceptance = (Order Received + CBS Orders) / Incoming Orders
static double calculateBidAcceptance({
  required int orderReceived,
  required int cbsOrder,
  required int incomingOrder,
}) {
  if (incomingOrder == 0) return 0;
  final double result = (orderReceived + cbsOrder) / incomingOrder;
  return (result * 100).ceil() / 100; // Rounded to 2 decimal places
}
```

#### Trip Completion Rate Calculation
```dart
// Formula: Trip Completion = Completed Orders / Order Received
static double calculateTripCompletion({
  required int orderCompleted,
  required int orderReceived,
}) {
  if (orderReceived == 0) return 0;
  final double result = orderCompleted / orderReceived;
  return (result * 100).ceil() / 100; // Rounded to 2 decimal places
}
```

#### Total Income Calculation
```dart
// Formula: Total Income = Trip Earnings + Bonus + Tips
static double calculateTotalIncome({
  required double trip,
  required double bonus,
  required double tips,
}) {
  return trip + bonus + tips;
}
```

### 3. Performance Analytics Calculations

#### Average Completed Orders Per Day
```dart
// Formula: Average Completed = Total Completed Orders / Active Days
static double calculateAvgCompleted({
  required int sumCompleted,
  required int activeDays,
}) {
  if (activeDays == 0) return 0;
  return sumCompleted / activeDays;
}
```

**Source of `sumCompleted`:**
The `sumCompleted` value is obtained from the **last 14 days of order data** (excluding the current day). This calculation is performed by:

1. **Date Range Calculation**: 
   ```dart
   // If endDate is March 25, 2025, we want orders from March 11, 2025 to March 24, 2025
   final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
   final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total
   ```

2. **Database Query**: 
   ```dart
   Future<Either<Failure, List<Order>>> getOrdersForPerformanceCalculation(DateTime endDate) async {
     // Calculate the date range for the last 14 days (not including the current date)
     final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
     final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total
     
     return await getOrdersForDateRange(startDate, adjustedEndDate);
   }
   ```

3. **Sum Calculation**:
   ```dart
   Future<Either<Failure, int>> getTotalCompletedOrdersForLast14Days(DateTime endDate) async {
     final ordersResult = await getOrdersForPerformanceCalculation(endDate);
     
     return ordersResult.fold(
       (failure) => Left(failure),
       (orders) {
         // Calculate total completed orders
         int totalOrdersCompleted = 0;
         for (final order in orders) {
           totalOrdersCompleted += order.orderCompleted;
         }
         return Right(totalOrdersCompleted);
       },
     );
   }
   ```

**Business Logic:**
- **14-Day Window**: Uses the last 14 days of order data to provide a stable performance baseline
- **Excludes Current Day**: The current day is excluded to avoid incomplete data
- **Performance Baseline**: This 14-day window serves as the foundation for calculating average performance metrics
- **Real-time Updates**: The calculation is updated whenever new order data is entered

#### Average Online Hours Per Day
```dart
// Formula: Average Online = Total Online Hours / Active Days
static double calculateAvgOnline({
  required double onlineHours,
  required int activeDays,
}) {
  if (activeDays == 0) return 0;
  return onlineHours / activeDays;
}
```

#### Retention Rate Calculation
```dart
// Formula: Retention = (Average Online Hours * 60) / Average Completed Orders
static double calculateRetention({
  required double avgOnline,
  required double avgCompleted,
}) {
  if (avgCompleted == 0) return 0;
  return (avgOnline * 60) / avgCompleted;
}
```

**Business Logic:**
- Measures how efficiently the driver uses online time
- Higher retention indicates better order completion efficiency
- Used for level progression and performance evaluation

### 4. Spare Parts Management Calculations

**Important Note: Spare parts calculations depend on mileage data from the Income table.**

#### Mileage Data Dependency
The spare parts system relies on the **Income table** for current vehicle mileage data:

```dart
// Get the highest mileage from income table
Future<Either<Failure, int>> getHighestMileage() async {
  try {
    // Use a custom query to get the maximum final_mileage value
    final result = await database.customSelect(
      'SELECT MAX(final_mileage) as maxMileage FROM income',
    ).getSingleOrNull();

    if (result == null || result.data['maxMileage'] == null) {
      return const Right(0); // Default to 0 if no records found
    }

    final maxMileage = result.data['maxMileage'] as int;
    return Right(maxMileage);
  } catch (e) {
    return Left(Failure.database(e.message));
  }
}
```

**Dependency Flow:**
1. **Income Entry**: User enters daily income with `final_mileage`
2. **Mileage Update**: Spare parts system automatically updates current mileage
3. **Usage Calculation**: All spare parts calculations use this updated mileage
4. **Warning System**: Maintenance alerts are based on calculated usage

#### Usage Mileage Calculation
```dart
// Formula: Usage Mileage = Current Mileage - Initial Mileage
int usageMileage = currentMileage - initialMileage;
```

**Data Sources:**
- **Current Mileage**: Retrieved from `MAX(final_mileage)` in Income table
- **Initial Mileage**: Stored when spare part is installed
- **Usage Mileage**: Calculated difference for maintenance tracking

#### Usage Percentage Calculation
```dart
// Formula: Usage Percentage = (Usage Mileage / Mileage Limit) * 100
double usagePercent = (usageMileage / mileageLimit) * 100;
```

**Business Logic:**
- **Mileage Limit**: Set by user when installing spare part
- **Usage Percentage**: Determines maintenance urgency
- **Warning Threshold**: 80% triggers maintenance alerts

#### Days in Use Calculation
```dart
// Formula: Days in Use = Current Date - Installation Date
int daysInUse = DateTime.now().difference(installationDate).inDays;
```

#### Warning Status Logic
```dart
// Warning triggered when usage percentage exceeds 80%
bool warningStatus = usagePercent >= 80.0;
```

#### Automatic Mileage Updates

**Auto-Update Process:**
```dart
// Automatically update mileage when spare parts screen is loaded
Future<void> _autoUpdateMileage() async {
  try {
    // Get the highest mileage from income
    final repository = ref.read(enhancedSparePartsRepositoryProvider);
    final mileageResult = await repository.getHighestMileage();

    mileageResult.fold(
      (failure) {
        debugPrint('Error getting highest mileage: ${failure.toString()}');
      },
      (mileage) async {
        if (mileage > 0) {
          // Update all spare parts with the latest mileage
          final updateResult = await repository.updateAllPartsMileage(mileage);
          
          updateResult.fold(
            (failure) {
              debugPrint('Error updating mileage: ${failure.toString()}');
            },
            (success) {
              // Refresh the list to show updated mileage
              ref.invalidate(enhancedSparePartsListProvider);
            },
          );
        }
      },
    );
  } catch (e) {
    debugPrint('Error in auto-updating mileage: $e');
  }
}
```

**Update Triggers:**
1. **Screen Load**: When spare parts screen is opened
2. **Manual Refresh**: When user pulls to refresh
3. **Income Entry**: When new income record is added
4. **Background Sync**: During data synchronization

#### Spare Part Installation Process

**Initial Mileage Assignment:**
```dart
// When adding new spare part, use current vehicle mileage
Future<Either<Failure, EnhancedSparePart>> execute({
  required String partName,
  required String partType,
  required double price,
  required int mileageLimit,
  required DateTime installationDate,
  int? initialMileage,
  String notes = '',
}) async {
  try {
    // If initial mileage is not provided, get the highest mileage from income
    int actualInitialMileage = initialMileage ?? 0;
    if (initialMileage == null) {
      final mileageResult = await repository.getHighestMileage();
      actualInitialMileage = mileageResult.fold(
        (failure) => 0, // Default to 0 if there's an error
        (mileage) => mileage,
      );
    }

    // Create the spare part entity with computed values
    final sparePart = EnhancedSparePart.withComputedValues(
      partName: partName,
      partType: partType,
      price: price,
      mileageLimit: mileageLimit,
      initialMileage: actualInitialMileage,
      installationDate: installationDate,
      currentMileage: actualInitialMileage, // Initially, current = initial
      warningStatus: false,
      notes: notes,
      replacementCount: 0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return await repository.saveSparePart(sparePart);
  } catch (e) {
    return Left(Failure.businessLogic('Error adding spare part: $e'));
  }
}
```

#### Replacement Process

**Mileage at Replacement:**
```dart
// When replacing spare part, use current vehicle mileage
Future<Either<Failure, ReplacementResult>> replaceSparePart({
  required EnhancedSparePart oldPart,
  required DateTime replacementDate,
  required int currentMileage, // From income table
  required double newPartPrice,
  String? newPartName,
  String? newPartType,
  int? newMileageLimit,
  String replacementReason = 'Regular maintenance',
  String notes = '',
}) async {
  // Save replacement history with current mileage
  final historyResult = await saveReplacementHistory(
    partName: oldPart.partName,
    partType: oldPart.partType,
    price: oldPart.price,
    replacementDate: replacementDate,
    mileageAtReplacement: currentMileage, // Current vehicle mileage
    sparePartId: oldPart.id!,
    installationDate: oldPart.installationDate,
    initialMileage: oldPart.initialMileage,
    replacementReason: replacementReason,
    notes: notes,
  );

  // Create new spare part with current mileage as initial
  final updatedPart = EnhancedSparePart.withComputedValues(
    id: oldPart.id,
    partName: newPartName ?? oldPart.partName,
    partType: newPartType ?? oldPart.partType,
    price: newPartPrice,
    mileageLimit: newMileageLimit ?? oldPart.mileageLimit,
    initialMileage: currentMileage, // Current mileage becomes new initial
    installationDate: replacementDate,
    currentMileage: currentMileage,
    warningStatus: false,
    replacementCount: oldPart.replacementCount + 1,
    notes: notes,
    createdAt: oldPart.createdAt,
    updatedAt: DateTime.now(),
  );

  return await updateSparePart(updatedPart);
}
```

#### Data Flow Summary

**Income → Spare Parts Flow:**
1. **User enters income** with `final_mileage` value
2. **System queries** `MAX(final_mileage)` from Income table
3. **Spare parts updated** with new current mileage
4. **Usage calculations** recalculated for all parts
5. **Warning status** updated based on new usage percentages
6. **UI refreshed** to show updated maintenance status

**Dependency Chain:**
```
Income Table (final_mileage) 
    ↓
Spare Parts Repository (getHighestMileage)
    ↓
Spare Parts Entity (currentMileage)
    ↓
Usage Calculations (usageMileage, usagePercent)
    ↓
Warning System (warningStatus)
    ↓
UI Display (maintenance alerts)
```

### 5. Level System Calculations

#### Level Requirements System
The application uses a tiered level system with four levels:
- **Basic**: Entry level for all drivers
- **Silver**: First achievement level
- **Gold**: Advanced level
- **Platinum**: Highest level

#### Level Evaluation Criteria
Each level requires three metrics to be met:
1. **Total Points**: Accumulated points for the current month
2. **Bid Acceptance Rate**: Percentage of accepted bids
3. **Trip Completion Rate**: Percentage of completed trips

#### Default Level Requirements
```dart
// Default requirements for each level
Platinum: 500 points, 95% bid acceptance, 98% trip completion
Gold: 350 points, 90% bid acceptance, 95% trip completion  
Silver: 200 points, 85% bid acceptance, 90% trip completion
Basic: No minimum requirements
```

#### Level Evaluation Algorithm
```dart
// Level determination logic
if (totalPoints >= platinumPointsReq && 
    bidAcceptance >= platinumBidReq && 
    tripCompletion >= platinumTripReq) {
  return DriverLevel.platinum;
} else if (totalPoints >= goldPointsReq && 
           bidAcceptance >= goldBidReq && 
           tripCompletion >= goldTripReq) {
  return DriverLevel.gold;
} else if (totalPoints >= silverPointsReq && 
           bidAcceptance >= silverBidReq && 
           tripCompletion >= silverTripReq) {
  return DriverLevel.silver;
} else {
  return DriverLevel.basic;
}
```

#### Points Calculation System

**Monthly Points Calculation:**
```dart
// Points are calculated for the current month only
Future<Either<Failure, int>> getTotalPointsForMonth(int year, int month) async {
  final startDate = DateTime(year, month, 1);
  final endDate = DateTime(year, month + 1, 0); // Last day of the month
  
  final orders = await getOrdersForDateRange(startDate, endDate);
  
  int totalPoints = 0;
  for (final order in orders) {
    totalPoints += order.points.toInt();
  }
  return Right(totalPoints);
}
```

**Points Per Order:**
- Each completed order earns **150 points**
- Points are manually entered by the driver for each day
- Total monthly points = Sum of all daily points for the current month

#### Bid Acceptance Rate Calculation

**Formula:**
```dart
Bid Acceptance Rate = (Orders Received + CBS Orders) / Total Incoming Orders
```

**Calculation Process:**
1. **Total Incoming Orders**: Sum of all incoming orders for the current month
2. **Orders Received**: Sum of completed + canceled orders for the current month
3. **CBS Orders**: Sum of CBS orders for the current month
4. **Rate Calculation**: `(totalOrdersReceived + totalCbsOrders) / totalIncomingOrders`

#### Trip Completion Rate Calculation

**Formula:**
```dart
Trip Completion Rate = Orders Completed / Orders Received
```

**Calculation Process:**
1. **Orders Completed**: Sum of all completed orders for the current month
2. **Orders Received**: Sum of completed + canceled orders for the current month
3. **Rate Calculation**: `totalOrdersCompleted / totalOrdersReceived`

#### Level Progress Calculations

**Orders Needed for Bid Acceptance:**
```dart
int calculateOrdersNeededForBidAcceptance(double targetRate) {
  if (bidAcceptance >= targetRate) return 0;
  
  // Calculate target rate minus based on target
  double targetRateMinus;
  if (targetRate <= 0.90) targetRateMinus = 0.89;
  else if (targetRate <= 0.92) targetRateMinus = 0.91;
  else if (targetRate <= 0.94) targetRateMinus = 0.9301;
  else if (targetRate <= 0.95) targetRateMinus = 0.94004;
  else if (targetRate <= 0.96) targetRateMinus = 0.9501;
  else if (targetRate <= 0.97) targetRateMinus = 0.96005;
  else if (targetRate <= 0.98) targetRateMinus = 0.97001;
  else if (targetRate <= 0.99) targetRateMinus = 0.98001;
  else targetRateMinus = 0.990004;
  
  // Formula: (targetRateMinus * totalIncomingOrders - (totalOrdersReceived + totalCbsOrders)) / (1 - targetRateMinus)
  final double ordersNeeded = (targetRateMinus * totalIncomingOrders - (totalOrdersReceived + totalCbsOrders)) / (1 - targetRateMinus);
  
  return math.max(1, ordersNeeded.ceil());
}
```

**Orders Needed for Trip Completion:**
```dart
int calculateOrdersNeededForTripCompletion(double targetRate) {
  if (tripCompletion >= targetRate) return 0;
  
  // Calculate target rate minus based on target
  double targetRateMinus;
  if (targetRate <= 0.90) targetRateMinus = 0.89000;
  else if (targetRate <= 0.92) targetRateMinus = 0.91000;
  else if (targetRate <= 0.94) targetRateMinus = 0.93010;
  else if (targetRate <= 0.95) targetRateMinus = 0.94004;
  else if (targetRate <= 0.96) targetRateMinus = 0.95010;
  else if (targetRate <= 0.97) targetRateMinus = 0.96005;
  else if (targetRate <= 0.98) targetRateMinus = 0.97001;
  else if (targetRate <= 0.99) targetRateMinus = 0.98001;
  else targetRateMinus = 0.99000;
  
  // Formula: (targetRateMinus * totalOrdersReceived - totalOrdersCompleted) / (1 - targetRateMinus)
  final double ordersNeeded = (targetRateMinus * totalOrdersReceived - totalOrdersCompleted) / (1 - targetRateMinus);
  
  return ordersNeeded.ceil();
}
```

**Orders Needed for Points:**
```dart
// Calculate orders needed to reach target points
int ordersNeededForPoints = (pointsNeeded / pointsPerOrder).ceil();
// Where pointsPerOrder = 150
```

#### Level Benefits System

**Silver Level Benefits:**
- Priority access to orders during normal hours
- 5% bonus on completed orders
- Weekly performance insights

**Gold Level Benefits:**
- Priority access to orders during all hours
- 10% bonus on completed orders
- Weekly performance insights
- Dedicated support line

**Platinum Level Benefits:**
- Highest priority access to orders
- 15% bonus on completed orders
- Daily performance insights
- Premium dedicated support
- Exclusive promotions and events

#### Level Settings Management

**Configurable Requirements:**
- All level requirements can be modified by administrators
- Changes take effect immediately
- Settings are stored in the database
- Default values are provided if no settings exist

**Validation Rules:**
```dart
// Level progression validation
if (silverPoints >= goldPoints) {
  throw Error('Silver points must be less than Gold points');
}
if (goldPoints >= platinumPoints) {
  throw Error('Gold points must be less than Platinum points');
}
```

#### Real-time Level Updates

**Automatic Evaluation:**
- Level is recalculated every time order data is updated
- Current month data is used for all calculations
- Level changes are reflected immediately in the UI
- Progress towards next level is shown in real-time

**Calculation Triggers:**
1. **Order Entry**: When new order data is entered
2. **Order Update**: When existing order data is modified
3. **Order Deletion**: When order data is removed
4. **Settings Change**: When level requirements are modified

#### Points Accumulation
- **Daily Points**: Sum of all points earned from daily orders
- **Monthly Points**: Total points for the current month
- **Level Requirements**: Configurable points needed for each level

#### Bid Acceptance Rate for Levels
```dart
// Formula: Bid Acceptance = (Order Received + CBS Orders) / Incoming Orders
// Requirements vary by level:
// - Bronze: 85% minimum
// - Silver: 90% minimum  
// - Gold: 95% minimum
// - Platinum: 98% minimum
```

#### Trip Completion Rate for Levels
```dart
// Formula: Trip Completion = Completed Orders / Order Received
// Requirements vary by level:
// - Bronze: 80% minimum
// - Silver: 85% minimum
// - Gold: 90% minimum
// - Platinum: 95% minimum
```

### 6. Summary and Analytics Calculations

#### Income Summary Calculations
```dart
class IncomeSummary {
  // Total net income across selected period
  double totalNetIncome = sum(netIncome);
  
  // Total mileage across selected period
  int totalMileage = sum(mileage);
  
  // Income per mile rate
  double mileageRate = totalNetIncome / totalMileage;
  
  // Highest single-day income
  double highestIncome = max(netIncome);
}
```

#### Order Summary Calculations
```dart
class OrderSummary {
  // Average bid acceptance rate
  double avgBidAcceptance = sum(bidAcceptance) / count(orders);
  
  // Average trip completion rate
  double avgTripCompletion = sum(tripCompletion) / count(orders);
  
  // Average earnings per order
  double avgEarningsPerOrder = totalIncome / totalCompletedOrders;
  
  // Total points earned
  int totalPoints = sum(points);
}
```

#### Performance Summary Calculations
```dart
class PerformanceSummary {
  // Average performance metrics across date range
  double avgBidPerformance = sum(bidPerformance) / count(records);
  double avgTripPerformance = sum(tripPerformance) / count(records);
  
  // Total active days
  int totalActiveDays = sum(activeDays);
  
  // Average online hours per day
  double avgOnlineHours = sum(onlineHours) / count(records);
  
  // Overall retention rate
  double retention = (avgOnlineHours * 60) / avgCompletedOrders;
}
```

### 7. Real-time Calculation Triggers

#### Automatic Calculation Events
1. **Income Entry**: When user enters income data
   - Triggers: Net income, mileage, and payment method calculations
   - Updates: Income summary and trends

2. **Order Entry**: When user enters order data
   - Triggers: Bid acceptance, trip completion, and income calculations
   - Updates: Order summary and performance metrics

3. **Performance Entry**: When user enters performance data
   - Triggers: Average calculations and retention rate
   - Updates: Performance dashboard and level progress

4. **Spare Parts Updates**: When mileage is updated
   - Triggers: Usage calculations and warning status
   - Updates: Maintenance alerts and replacement schedules

5. **Level Progress**: When performance metrics change
   - Triggers: Level evaluation and progress calculations
   - Updates: Level display and requirements

### 8. 14-Day Performance Calculation Pattern

The application uses a consistent **14-day performance calculation pattern** for performance metrics. This pattern ensures stable and meaningful performance calculations.

#### 14-Day Window Logic
```dart
// Example: If today is March 25, 2025
final endDate = DateTime.now(); // March 25, 2025
final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // March 24, 2025
final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // March 11, 2025

// Result: 14-day window from March 11 to March 24, 2025
```

#### Performance Calculation Flow
1. **Date Range Setup**:
   ```dart
   // Calculate the date range for the last 14 days (not including the current date)
   final adjustedEndDate = endDate.subtract(const Duration(days: 1)); // Exclude current date
   final startDate = adjustedEndDate.subtract(const Duration(days: 13)); // 14 days total
   ```

2. **Order Data Retrieval**:
   ```dart
   // Get all orders within the 14-day window
   final orders = await orderRepository.getOrdersForDateRange(startDate, adjustedEndDate);
   ```

3. **Metrics Calculation**:
   ```dart
   // Calculate total completed orders from the 14-day window
   int totalCompletedOrders = 0;
   for (final order in orders) {
     totalCompletedOrders += order.orderCompleted;
   }
   
   // Calculate average completed orders per active day
   double avgCompleted = activeDays > 0 ? totalCompletedOrders / activeDays : 0;
   ```

#### Why 14 Days?
- **Stable Baseline**: 14 days provides enough data for meaningful averages
- **Performance Tracking**: Allows tracking of performance trends over time
- **Excludes Current Day**: Avoids incomplete data from the current day
- **Industry Standard**: Common practice in performance tracking applications

#### Active Days Calculation
```dart
// Count unique days with at least one order
final Set<DateTime> activeDaysSet = {};
for (final order in orders) {
  // Only count the date part (not time)
  final orderDate = DateTime(order.date.year, order.date.month, order.date.day);
  activeDaysSet.add(orderDate);
}
final activeDays = activeDaysSet.length;
```

#### Real-time Updates
- **Automatic Recalculation**: When new orders are added, the 14-day window shifts
- **Performance Tracking**: Users can see how their performance changes over time
- **Level Progression**: Level calculations use this 14-day baseline for consistent evaluation

### 9. Calculation Accuracy and Validation

#### Input Validation
- **Range Checks**: Ensures values are within reasonable bounds
- **Type Validation**: Validates data types and formats
- **Required Fields**: Ensures all necessary data is provided

#### Calculation Validation
- **Division by Zero**: Handles cases where denominators are zero
- **Rounding**: Consistent rounding to 2 decimal places for percentages
- **Precision**: Maintains appropriate precision for financial calculations

#### Error Handling
- **Calculation Errors**: Graceful handling of calculation failures
- **Data Integrity**: Ensures calculated values are consistent
- **User Feedback**: Clear error messages for invalid inputs

### 10. Performance Optimization

#### Caching Strategies
- **Summary Caching**: Caches calculated summaries to avoid recalculation
- **Incremental Updates**: Only recalculates affected data
- **Background Processing**: Performs heavy calculations in background

#### Calculation Efficiency
- **Batch Processing**: Groups calculations for better performance
- **Lazy Loading**: Calculates values only when needed
- **Memory Management**: Efficient memory usage for large datasets

### 11. Business Rules and Constraints

#### Financial Calculations
- **Currency Precision**: All monetary values rounded to 2 decimal places
- **Negative Values**: Handles negative income scenarios appropriately
- **Zero Values**: Proper handling of zero amounts and percentages

#### Performance Metrics
- **Minimum Thresholds**: Ensures meaningful calculations with sufficient data
- **Maximum Limits**: Prevents unrealistic values from skewing averages
- **Time-based Calculations**: Considers date ranges for accurate period analysis

#### Level Progression
- **Cumulative Requirements**: Tracks progress across multiple criteria
- **Minimum Periods**: Ensures sustained performance before level advancement
- **Grace Periods**: Allows for temporary performance dips without level loss

---

## UI/UX Design

### Design System Overview

The bidtrakr application follows a comprehensive design system that ensures consistency, accessibility, and excellent user experience across all features. The design system is built on Material Design 3 principles with custom adaptations for the driver performance tracking context.

#### Design Philosophy
- **Driver-Centric**: Optimized for drivers who need quick access to information while on the go
- **Data-Rich**: Efficiently displays complex performance metrics in digestible formats
- **Accessible**: Supports various screen sizes and accessibility needs
- **Responsive**: Adapts to different device orientations and screen sizes

### Color System

#### Primary Color Palette
```dart
class AppColors {
  // Primary Brand Colors
  static const Color primary = Color(0xFF3A87AD);      // Main brand blue-teal
  static const Color primaryLight = Color(0xFF5BA7C9); // Light variant
  static const Color primaryDark = Color(0xFF286A8A);  // Dark variant
  
  // Secondary Colors
  static const Color secondary = Color(0xFFD68C45);    // Warm accent
  static const Color secondaryLight = Color(0xFFE6A96A);
  static const Color secondaryDark = Color(0xFFB57035);
}
```

#### Status Colors
```dart
// Status Indicators
static const Color success = Color(0xFF4CAF7C);    // Green for success
static const Color warning = Color(0xFFEDAB4A);    // Amber for warnings
static const Color error = Color(0xFFE05D65);      // Red for errors
static const Color info = Color(0xFF4A9CC9);       // Blue for info
```

#### Level System Colors
```dart
// Driver Level Colors
static const Color basicLevelPrimary = Color(0xFF0DA802);    // Green
static const Color silverLevelPrimary = Color(0xFFA3A5A4);   // Silver
static const Color goldLevelPrimary = Color(0xFFFFB31F);     // Gold
static const Color platinumLevelPrimary = Color(0xFF5A6875); // Platinum
```

#### Text Colors
```dart
// Typography Colors
static const Color textPrimary = Color(0xFF1A1A1A);    // Main text
static const Color textSecondary = Color(0xFF616161);   // Secondary text
static const Color textLight = Color(0xFFFFFFFF);       // Light text
static const Color textDisabled = Color(0xFFBDBDBD);    // Disabled text
```

#### Background Colors
```dart
// Surface Colors
static const Color background = Color(0xFFF8F9FA);      // App background
static const Color surface = Color(0xFFFFFFFF);         // Card surface
static const Color cardBackground = Color(0xFFFFFFFF);  // Card background
static const Color divider = Color(0xFFE0E0E0);        // Dividers
```

### Typography System

#### Font Family
- **Primary Font**: Google Fonts Poppins
- **Fallback**: System default sans-serif
- **Weights**: Normal (400), Medium (500), Bold (700)

#### Text Scale
```dart
// Typography Scale
static const double fontSizeH1 = 20;      // Large headings
static const double fontSizeH2 = 17;      // Medium headings
static const double fontSizeH3 = 15;      // Small headings
static const double fontSizeBodyLarge = 14; // Body text
static const double fontSizeBodyMedium = 12; // Secondary text
static const double fontSizeBodySmall = 10;  // Captions
static const double fontSizeButton = 12;     // Button text
static const double fontSizeLabel = 12;      // Form labels
```

#### Text Styles
```dart
// Heading Styles
displayLarge: GoogleFonts.poppins(
  fontSize: 20,
  fontWeight: FontWeight.bold,
  color: AppColors.textPrimary,
),

// Body Styles
bodyLarge: GoogleFonts.poppins(
  fontSize: 14,
  fontWeight: FontWeight.normal,
  color: AppColors.textPrimary,
),

// Label Styles
labelLarge: GoogleFonts.poppins(
  fontSize: 12,
  fontWeight: FontWeight.w500,
  color: AppColors.textPrimary,
),
```

### Spacing System

#### Responsive Spacing
```dart
class AppDimensions {
  // 4-point Grid System
  static double get spacing2 => blockSizeHorizontal * 0.5;   // 2px
  static double get spacing4 => blockSizeHorizontal * 1.0;   // 4px
  static double get spacing8 => blockSizeHorizontal * 2.0;   // 8px
  static double get spacing12 => blockSizeHorizontal * 3.0;  // 12px
  static double get spacing16 => blockSizeHorizontal * 4.0;  // 16px
  static double get spacing20 => blockSizeHorizontal * 5.0;  // 20px
  static double get spacing24 => blockSizeHorizontal * 6.0;  // 24px
  static double get spacing32 => blockSizeHorizontal * 8.0;  // 32px
}
```

#### Component Spacing
```dart
// Standard Paddings
static EdgeInsets get paddingScreen => EdgeInsets.all(spacing16);
static EdgeInsets get paddingCard => EdgeInsets.all(spacing16);
static EdgeInsets get paddingListItem => EdgeInsets.symmetric(
  horizontal: spacing16,
  vertical: spacing8,
);
static EdgeInsets get paddingButton => EdgeInsets.symmetric(
  horizontal: spacing16,
  vertical: spacing12,
);
```

### Component Library

#### Button Components
```dart
enum AppButtonType {
  primary,    // Filled primary button
  secondary,  // Filled secondary button
  outline,    // Outlined button
  text,       // Text button
  danger      // Destructive action button
}

enum AppButtonSize {
  small,      // Compact button
  medium,     // Standard button
  large       // Prominent button
}
```

**Button Usage Examples:**
```dart
// Primary Action Button
AppButton(
  text: 'Save Income',
  type: AppButtonType.primary,
  size: AppButtonSize.medium,
  onPressed: () => saveIncome(),
)

// Secondary Action Button
AppButton(
  text: 'Cancel',
  type: AppButtonType.outline,
  size: AppButtonSize.medium,
  onPressed: () => Navigator.pop(context),
)
```

#### Card Components
```dart
enum AppCardType {
  standard,   // Default card
  elevated,   // Card with shadow
  outlined,   // Card with border
  filled      // Card with background
}
```

**Card Usage Examples:**
```dart
// Standard Information Card
AppCard(
  type: AppCardType.standard,
  child: Column(
    children: [
      Text('Income Summary'),
      Text('Total: \$1,250'),
    ],
  ),
)

// Elevated Status Card
AppCard(
  type: AppCardType.elevated,
  child: Column(
    children: [
      Icon(Icons.trending_up, color: AppColors.success),
      Text('Performance Improved'),
    ],
  ),
)
```

#### List Item Components
```dart
// Standard List Item
AppListItem(
  title: 'Daily Income',
  subtitle: 'March 15, 2024',
  leadingIcon: Icons.attach_money,
  trailing: Text('\$85.50'),
  onTap: () => viewDetails(),
)

// List Item with Badge
AppListItem(
  title: 'Engine Oil',
  subtitle: 'Last replaced: 2 months ago',
  leadingIcon: Icons.build,
  badgeText: 'Warning',
  badgeColor: AppColors.warning,
  onTap: () => viewMaintenance(),
)
```

### Layout Patterns

#### Screen Layout Structure
```dart
// Standard Screen Layout
Scaffold(
  appBar: AppBar(
    title: Text('Screen Title'),
    actions: [SyncStatusIndicator()],
  ),
  body: Column(
    children: [
      // Summary Section
      SummaryCardsSection(),
      
      // Main Content
      Expanded(
        child: RefreshIndicator(
          onRefresh: () => refreshData(),
          child: MainContentList(),
        ),
      ),
    ],
  ),
  floatingActionButton: FloatingActionButton(
    onPressed: () => addNewRecord(),
    child: Icon(Icons.add),
  ),
)
```

#### Navigation Patterns

**Bottom Navigation:**
```dart
BottomNavigationBar(
  type: BottomNavigationBarType.fixed,
  items: [
    BottomNavigationBarItem(
      icon: Icon(Icons.attach_money),
      label: 'Income',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.list),
      label: 'Orders',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.trending_up),
      label: 'Performance',
    ),
    BottomNavigationBarItem(
      icon: Icon(Icons.more_horiz),
      label: 'More',
    ),
  ],
)
```

**Tab Navigation:**
```dart
TabBar(
  tabs: [
    Tab(text: 'Today'),
    Tab(text: 'This Week'),
    Tab(text: 'This Month'),
  ],
)
```

### Screen-Specific Layouts

The bidtrakr application uses a **bottom navigation bar** as the primary navigation mechanism, with four main tabs:
- **Income**
- **Orders**
- **Performance**
- **More**

Each tab represents a separate screen with its own layout and logic. There is **no composite dashboard home screen**; instead, users access each main feature directly via the bottom navigation.

Below are the actual layouts for each main screen:

#### 1. Income Screen
- **AppBar**: Includes a date range selector.
- **Summary Section**: Displays income summary cards (e.g., net income, mileage).
- **Main Content**: List of income records, paginated and filterable by date range.
- **Pull-to-Refresh**: Supported via `RefreshIndicator`.
- **No floating action button** (add income is handled via other UI elements).

**Code Structure Example:**
```dart
Scaffold(
  body: RefreshIndicator(
    onRefresh: ...,
    child: CustomScrollView(
      slivers: [
        _buildAppBar(dateRangeAsync),
        // Summary section
        _buildSummarySection(incomeSummary),
        // History header
        _buildHistoryHeader(),
        // Paginated income list
        _buildPaginatedIncomeListSection(paginatedIncomeAsync),
      ],
    ),
  ),
)
```

#### 2. Orders Screen
- **AppBar**: Includes a date range selector.
- **Summary Section**: Displays order metrics and statistics.
- **Main Content**: List of order records, filterable by date range.
- **Pull-to-Refresh**: Supported via `RefreshIndicator`.
- **No floating action button**.

**Code Structure Example:**
```dart
Scaffold(
  body: RefreshIndicator(
    onRefresh: ...,
    child: CustomScrollView(
      slivers: [
        _buildAppBar(dateRangeAsync),
        _buildSummarySection(context, orderSummaryAsync),
        _buildHistoryHeader(),
        _buildOrderListSection(orderListAsync),
      ],
    ),
  ),
)
```

#### 3. Performance Screen
- **AppBar**: Includes a date range selector.
- **Summary Section**: Displays performance metrics and charts.
- **Main Content**: List of performance records.
- **Pull-to-Refresh**: Supported via `RefreshIndicator`.
- **No floating action button**.

**Code Structure Example:**
```dart
Scaffold(
  body: RefreshIndicator(
    onRefresh: ...,
    child: CustomScrollView(
      slivers: [
        _buildAppBar(context, ref, dateRangeAsync),
        _buildHeaderSection(context),
        _buildPerformanceList(context, ref, performanceList),
      ],
    ),
  ),
)
```

#### 4. More Screen
- **AppBar**: Title and optional actions (e.g., sync status).
- **Main Content**: ListView with user info, feature cards, settings, and footer.
- **No summary cards or floating action button**.

**Code Structure Example:**
```dart
Scaffold(
  appBar: _buildAppBar(),
  body: ListView(
    padding: EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 24.0),
    children: [
      UserInfoCard(),
      // Features section
      _buildFeaturesCard(context, ref),
      // Settings section
      _buildSettingsCard(context),
      // Footer
      AppFooter(),
    ],
  ),
)
```

#### Navigation Structure
- The **bottom navigation bar** is present at the root level (`HomeScreen`), not within each feature screen.
- Each tab is a separate screen; switching tabs swaps the entire content area.
- There is no unified dashboard or composite home screen as previously described in the documentation.

**Navigation Example:**
```dart
class HomeScreen extends ConsumerStatefulWidget {
  // ...
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        // ...
      ),
    );
  }
}
```

---

## Bottom Navigation System

### Overview
The bottom navigation bar is the primary navigation mechanism in the bidtrakr app, allowing users to quickly switch between the main feature areas: Income, Orders, Performance, and More. It is always visible on the main screens, providing a consistent and intuitive way to access core workflows.

### Purpose and Role
- **Central Navigation**: Provides direct access to the four main modules.
- **Context Awareness**: Highlights the current section for user orientation.
- **Workflow Entry Point**: Each tab serves as the starting point for its respective feature's workflow.

### Technical Implementation
- **Widget**: Uses Flutter's `BottomNavigationBar` widget with `BottomNavigationBarType.fixed` for stable tab positions.
- **Integration**: Typically placed in the `Scaffold` of the main navigation screen (e.g., HomeScreen or MainWrapper).
- **State Management**: The selected tab index is managed using Riverpod providers for reactive updates and testability.
- **Navigation Logic**: Tapping a tab updates the selected index and swaps the main content area to the corresponding feature screen.
- **Deep Linking**: Can be extended to support deep links and programmatic navigation.

#### Example Implementation
```dart
final bottomNavIndexProvider = StateProvider<int>((ref) => 0);

class MainScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final index = ref.watch(bottomNavIndexProvider);
    return Scaffold(
      body: IndexedStack(
        index: index,
        children: [
          IncomeScreen(),
          OrdersScreen(),
          PerformanceScreen(),
          MoreScreen(),
        ],
      ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: index,
        onTap: (i) => ref.read(bottomNavIndexProvider.notifier).state = i,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.attach_money), label: 'Income'),
          BottomNavigationBarItem(icon: Icon(Icons.list), label: 'Orders'),
          BottomNavigationBarItem(icon: Icon(Icons.trending_up), label: 'Performance'),
          BottomNavigationBarItem(icon: Icon(Icons.more_horiz), label: 'More'),
        ],
      ),
    );
  }
}
```

### Customization & Theming
- **Colors**: The navigation bar and icons use theme colors for background, selected, and unselected states.
- **Icons & Labels**: Each tab uses a meaningful icon and label for clarity.
- **Adaptive Design**: The bar adapts to light/dark themes and device safe areas.
- **Badge Support**: Can be extended to show badges (e.g., notifications) on tabs.

#### Example Theming
```dart
BottomNavigationBar(
  backgroundColor: Theme.of(context).bottomAppBarColor,
  selectedItemColor: AppColors.primary,
  unselectedItemColor: AppColors.textSecondary,
  // ...
)
```

### Accessibility Considerations
- **Semantic Labels**: Each tab provides semantic labels for screen readers.
- **Touch Targets**: Tabs meet minimum size requirements for accessibility.
- **Contrast**: Ensures sufficient color contrast for selected/unselected states.
- **Focus Management**: Supports keyboard navigation and focus highlights.

### Extensibility
- **Adding Tabs**: Easily add new tabs by updating the provider and navigation logic.
- **Feature Integration**: Each tab can host its own navigation stack (using nested Navigators) for complex flows.
- **Platform Adaptation**: Can be replaced with platform-specific navigation (e.g., CupertinoTabBar) for iOS.

### Best Practices
- Keep the number of tabs between 3-5 for clarity.
- Use descriptive icons and labels.
- Maintain state when switching tabs (use `IndexedStack`).
- Avoid deep navigation in tab content; use modal or push for detail screens.
- Test for accessibility and responsiveness on all devices.

### Troubleshooting
- **State Loss**: Use `IndexedStack` to preserve state of each tab.
- **Navigation Conflicts**: Use nested navigators for independent tab navigation stacks.
- **Performance**: Avoid heavy widget rebuilds by using providers and efficient widget trees.

---

## Technical Implementation

### State Management Architecture

#### Provider Structure
```dart
// Global providers
final databaseProvider = Provider<AppDatabase>((ref) => ...);
final authServiceProvider = Provider<AuthService>((ref) => ...);
final syncServiceProvider = Provider<SyncService>((ref) => ...);

// Feature-specific providers
final incomeListProvider = StateNotifierProvider<IncomeNotifier, AsyncValue<List<Income>>>((ref) => ...);
final orderListProvider = StateNotifierProvider<OrderNotifier, AsyncValue<List<Order>>>((ref) => ...);
final performanceListProvider = StateNotifierProvider<PerformanceNotifier, AsyncValue<List<Performance>>>((ref) => ...);
```

#### Repository Pattern
```dart
abstract class IncomeRepository {
  Future<Either<Failure, List<Income>>> getAllIncome();
  Future<Either<Failure, Income>> getIncomeById(int id);
  Future<Either<Failure, int>> insertIncome(Income income);
  Future<Either<Failure, bool>> updateIncome(Income income);
  Future<Either<Failure, bool>> deleteIncome(int id);
}
```

### Database Operations

#### CRUD Operations
```dart
class IncomeRepositoryImpl implements IncomeRepository {
  final AppDatabase _database;
  
  @override
  Future<Either<Failure, List<Income>>> getAllIncome() async {
    try {
      final incomeList = await _database.getAllIncome();
      return Right(incomeList.map((data) => _mapFromData(data)).toList());
    } on DatabaseException catch (e) {
      return Left(Failure.database(e.message));
    } catch (e) {
      return Left(Failure.unexpected(e.toString()));
    }
  }
}
```

#### Migration Strategy
```dart
@override
MigrationStrategy get migration {
  return MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();
      await Triggers.createTriggers(executor);
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // Handle schema upgrades
    },
    beforeOpen: (details) async {
      // Initialize default settings
    },
  );
}
```

### Error Handling

#### Failure Types
```dart
abstract class Failure {
  const Failure([this.message]);
  final String? message;
  
  const factory Failure.database(String message) = DatabaseFailure;
  const factory Failure.network(String message) = NetworkFailure;
  const factory Failure.unexpected(String message) = UnexpectedFailure;
  const factory Failure.notFound(String message) = NotFoundFailure;
}
```

#### Error Recovery
- **Automatic Retry**: Network operation retry mechanisms
- **Graceful Degradation**: Fallback to local data
- **User Feedback**: Clear error messages and recovery options

---

## Sync & Cloud Services

### Supabase Integration

#### Configuration
```dart
class SupabaseConfig {
  static const String url = 'YOUR_SUPABASE_URL';
  static const String anonKey = 'YOUR_SUPABASE_ANON_KEY';
  
  late final SupabaseClient client;
  bool _isInitialized = false;
  
  Future<void> initialize() async {
    await Supabase.initialize(
      url: url,
      anonKey: anonKey,
    );
    client = Supabase.instance.client;
    _isInitialized = true;
  }
}
```

#### Sync Operations
```dart
class SyncOperations {
  Future<void> syncData(SyncOperation operation) async {
    switch (operation) {
      case SyncOperation.upload:
        await _uploadPendingChanges();
        break;
      case SyncOperation.download:
        await _downloadRecentChanges();
        break;
      case SyncOperation.full:
        await _uploadPendingChanges();
        await _downloadRecentChanges();
        break;
    }
  }
}
```

### Sync Features

#### 1. Automatic Sync
- **Background Sync**: Periodic synchronization
- **Change Detection**: Automatic upload of local changes
- **Conflict Resolution**: Handle data conflicts intelligently

#### 2. Manual Sync
- **User-triggered**: Manual sync from UI
- **Progress Tracking**: Real-time sync progress
- **Status Updates**: Sync status indicators

#### 3. Offline Support
- **Local Database**: Full functionality offline
- **Queue Management**: Queue changes for later sync
- **Conflict Handling**: Resolve conflicts on reconnection

---

## Authentication & Security

### Authentication Flow

#### 1. User Registration
```dart
Future<AuthResponse> signUp({
  required String email,
  required String password,
  Map<String, dynamic>? data,
}) async {
  final response = await _repository.signUp(
    email: email,
    password: password,
    data: data,
  );
  return response;
}
```

#### 2. User Login
```dart
Future<AuthResponse> signIn({
  required String email,
  required String password,
}) async {
  final response = await _repository.signIn(
    email: email,
    password: password,
  );
  return response;
}
```

#### 3. Session Management
```dart
class SessionManager {
  Future<void> onAppResumed() async {
    await _checkAndRefreshSession();
  }
  
  Future<void> onSignOut() async {
    await _clearPersistedSession();
  }
}
```

### Security Features

#### 1. Data Encryption
- **Local Encryption**: Sensitive data encryption
- **Secure Storage**: Encrypted shared preferences
- **Token Management**: Secure token storage

#### 2. Network Security
- **HTTPS Only**: Secure communication with backend
- **Certificate Pinning**: Prevent man-in-the-middle attacks
- **Request Signing**: Authenticated API requests

#### 3. Privacy Protection
- **Data Minimization**: Only collect necessary data
- **User Control**: User can disable sync features
- **Local Storage**: Data remains on device by default

---

## Backup & Data Management

### Backup System

#### 1. Local Backup
```dart
class BackupService {
  Future<String> createBackup() async {
    final database = await _getDatabasePath();
    final backupPath = await _getBackupPath();
    
    await File(database).copy(backupPath);
    return backupPath;
  }
}
```

#### 2. Restore Functionality
```dart
Future<bool> restoreBackup(String backupPath) async {
  try {
    await _closeDatabase();
    await File(backupPath).copy(_getDatabasePath());
    await _reopenDatabase();
    return true;
  } catch (e) {
    return false;
  }
}
```

### Data Management Features

#### 1. Export Options
- **CSV Export**: Export data in CSV format
- **JSON Export**: Export data in JSON format
- **PDF Reports**: Generate PDF reports

#### 2. Data Validation
- **Integrity Checks**: Verify data consistency
- **Schema Validation**: Ensure data structure integrity
- **Backup Verification**: Validate backup files

#### 3. Cleanup Operations
- **Old Data Cleanup**: Remove outdated records
- **Cache Management**: Clear temporary data
- **Storage Optimization**: Optimize database size

---

## Development & Deployment

### Development Setup

#### 1. Prerequisites
```bash
# Install Flutter SDK
flutter doctor

# Install dependencies
flutter pub get

# Generate code
flutter packages pub run build_runner build
```

#### 2. Environment Configuration
```bash
# Create environment file
cp .env.example .env

# Configure Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### 3. Database Setup
```bash
# Generate database code
flutter packages pub run build_runner build --delete-conflicting-outputs

# Run migrations
flutter run
```

### Build Configuration

#### 1. Android Build
```gradle
android {
    compileSdkVersion 34
    
    defaultConfig {
        applicationId "com.englr.bidtrakr"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0.0"
    }
}
```

#### 2. iOS Build
```swift
// Info.plist configurations
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

#### 3. Web Build
```yaml
flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - .env
```

### Testing Strategy

#### 1. Unit Tests
```dart
void main() {
  group('IncomeRepository Tests', () {
    test('should return income list on success', () async {
      // Test implementation
    });
  });
}
```

#### 2. Widget Tests
```dart
void main() {
  testWidgets('IncomeScreen displays income list', (tester) async {
    await tester.pumpWidget(MyApp());
    expect(find.text('Income'), findsOneWidget);
  });
}
```

#### 3. Integration Tests
```dart
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  
  testWidgets('Complete income flow', (tester) async {
    // Test complete user flow
  });
}
```

### Deployment

#### 1. Release Build
```bash
# Android APK
flutter build apk --release

# iOS IPA
flutter build ios --release

# Web
flutter build web --release
```

#### 2. App Store Deployment
- **Android**: Google Play Store
- **iOS**: Apple App Store
- **Web**: Hosted web application

#### 3. CI/CD Pipeline
```yaml
# GitHub Actions example
name: Build and Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: subosito/flutter-action@v2
      - run: flutter test
```

---

## Application Initialization & Configuration

### Main Entry Point Configuration

#### 1. App Initialization Flow
```dart
void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the app using AppInitializer
  final dbPath = await AppInitializer.initialize();

  // Run the app with ProviderScope
  runApp(
    ProviderScope(
      overrides: [
        databasePathProvider.overrideWithValue(dbPath),
      ],
      child: const MyApp(),
    ),
  );
}
```

#### 2. AppInitializer Class
```dart
class AppInitializer {
  static Future<String> initialize() async {
    // Load environment variables
    await _loadEnvironmentVariables();
    
    // Configure database options
    _configureDatabaseOptions();
    
    // Get database path
    final dbPath = await _setupDatabase();
    
    // Check connectivity and initialize Supabase
    await _initializeSupabase();
    
    return dbPath;
  }
}
```

### Environment Configuration

#### 1. Environment Variables
```bash
# .env file
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### 2. Platform-Specific Configuration

**Android Configuration:**
```xml
<!-- AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

**iOS Configuration:**
```xml
<!-- Info.plist -->
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <true/>
</dict>
```

### Service Initialization

#### 1. Service Initializer
```dart
class ServiceInitializer {
  void initialize() {
    _initializeAuthService();
    _initializeSyncService();
  }
}
```

#### 2. Lifecycle Management
```dart
class LifecycleHandler extends ConsumerStatefulWidget {
  @override
  void initState() {
    super.initState();
    ref.read(serviceInitializerProvider).initializeWithPostFrameCallback();
    ref.read(appLifecycleServiceProvider).initialize();
  }
}
```

---

## Testing Strategy

### Unit Testing

#### 1. Repository Testing
```dart
void main() {
  group('IncomeRepository Tests', () {
    late MockAppDatabase mockDatabase;
    late IncomeRepository repository;

    setUp(() {
      mockDatabase = MockAppDatabase();
      repository = IncomeRepository(mockDatabase);
    });

    test('should return income list on success', () async {
      // Arrange
      final expectedIncome = [IncomeData(...)];
      when(mockDatabase.getAllIncome()).thenAnswer((_) async => expectedIncome);

      // Act
      final result = await repository.getAllIncome();

      // Assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (income) => expect(income.length, expectedIncome.length),
      );
    });

    test('should return database failure on error', () async {
      // Arrange
      when(mockDatabase.getAllIncome()).thenThrow(DatabaseException('DB Error'));

      // Act
      final result = await repository.getAllIncome();

      // Assert
      expect(result.isLeft(), true);
      result.fold(
        (failure) => expect(failure, isA<DatabaseFailure>()),
        (income) => fail('Should not return success'),
      );
    });
  });
}
```

#### 2. Service Testing
```dart
void main() {
  group('CalculationService Tests', () {
    late CalculationService calculationService;
    late MockCalculationStrategy mockStrategy;

    setUp(() {
      mockStrategy = MockCalculationStrategy();
      calculationService = CalculationService();
      calculationService.registerStrategy<Income>(mockStrategy);
    });

    test('should calculate income correctly', () async {
      // Arrange
      final income = Income(...);
      final expectedResult = Income(...);
      when(mockStrategy.execute(income, params: anyNamed('params')))
          .thenReturn(Right(expectedResult));

      // Act
      final result = calculationService.calculate(income);

      // Assert
      expect(result.isRight(), true);
      result.fold(
        (failure) => fail('Should not return failure'),
        (calculatedIncome) => expect(calculatedIncome, expectedResult),
      );
    });
  });
}
```

### Widget Testing

#### 1. Screen Testing
```dart
void main() {
  group('IncomeScreen Tests', () {
    testWidgets('displays income list correctly', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            incomeListProvider.overrideWith((ref) => AsyncValue.data([
              Income(...),
              Income(...),
            ])),
          ],
          child: MaterialApp(home: IncomeScreen()),
        ),
      );

      // Assert
      expect(find.text('Income Tracking'), findsOneWidget);
      expect(find.byType(IncomeCard), findsNWidgets(2));
    });

    testWidgets('shows loading state', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            incomeListProvider.overrideWith((ref) => const AsyncValue.loading()),
          ],
          child: MaterialApp(home: IncomeScreen()),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows error state with retry button', (tester) async {
      // Arrange
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            incomeListProvider.overrideWith((ref) => AsyncValue.error(
              'Network error',
              StackTrace.current,
            )),
          ],
          child: MaterialApp(home: IncomeScreen()),
        ),
      );

      // Assert
      expect(find.text('Error'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });
  });
}
```

#### 2. Component Testing
```dart
void main() {
  group('AppButton Tests', () {
    testWidgets('calls onPressed when tapped', (tester) async {
      // Arrange
      bool pressed = false;
      await tester.pumpWidget(
        MaterialApp(
          home: AppButton(
            text: 'Test Button',
            onPressed: () => pressed = true,
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Test Button'));

      // Assert
      expect(pressed, true);
    });

    testWidgets('shows loading state when isLoading is true', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: AppButton(
            text: 'Test Button',
            onPressed: () {},
            isLoading: true,
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Test Button'), findsNothing);
    });
  });
}
```

### Integration Testing

#### 1. Complete User Flow Testing
```dart
void main() {
  group('Income Flow Integration Tests', () {
    testWidgets('complete income entry flow', (tester) async {
      // Arrange
      await tester.pumpWidget(MyApp());

      // Act - Navigate to income screen
      await tester.tap(find.byIcon(Icons.attach_money));
      await tester.pumpAndSettle();

      // Act - Add new income
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Act - Fill form
      await tester.enterText(find.byKey(Key('initial_gopay')), '100000');
      await tester.enterText(find.byKey(Key('final_gopay')), '150000');
      await tester.enterText(find.byKey(Key('initial_mileage')), '1000');
      await tester.enterText(find.byKey(Key('final_mileage')), '1200');

      // Act - Save
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Income record saved successfully'), findsOneWidget);
      expect(find.text('50,000'), findsOneWidget); // Net income
    });
  });
}
```

### Mock Strategies

#### 1. Database Mocking
```dart
class MockAppDatabase extends Mock implements AppDatabase {
  @override
  Future<List<IncomeData>> getAllIncome() async {
    return [
      IncomeData(
        id: 1,
        date: DateTime.now(),
        initialMileage: 1000,
        finalMileage: 1200,
        // ... other fields
      ),
    ];
  }
}
```

#### 2. Network Mocking
```dart
class MockSupabaseClient extends Mock implements SupabaseClient {
  @override
  Future<PostgrestResponse> from(String table) async {
    return PostgrestResponse(
      data: [
        {'id': 1, 'date': '2024-01-01', 'amount': 50000},
      ],
      status: 200,
      statusText: 'OK',
    );
  }
}
```

---

## Error Handling & Logging

### Error Categorization

#### 1. Failure Types
```dart
@freezed
class Failure with _$Failure {
  const factory Failure.server([String? message]) = ServerFailure;
  const factory Failure.cache([String? message]) = CacheFailure;
  const factory Failure.network([String? message]) = NetworkFailure;
  const factory Failure.invalidInput([String? message]) = InvalidInputFailure;
  const factory Failure.database([String? message]) = DatabaseFailure;
  const factory Failure.businessLogic([String? message]) = BusinessLogicFailure;
  const factory Failure.notFound([String? message]) = NotFoundFailure;
  const factory Failure.unexpected([String? message]) = UnexpectedFailure;
}
```

#### 2. Exception Types
```dart
class AppException implements Exception {
  final String message;
  final String? code;
  
  AppException(this.message, {this.code});
}

class DatabaseException extends AppException {
  DatabaseException(String message, {String? code}) : super(message, code: code);
}

class NetworkException extends AppException {
  NetworkException(String message, {String? code}) : super(message, code: code);
}
```

### Logging Strategy

#### 1. Structured Logging
```dart
class AppLogger {
  static void log(String message, {
    LogLevel level = LogLevel.info,
    String? category,
    Map<String, dynamic>? metadata,
  }) {
    final logEntry = {
      'timestamp': DateTime.now().toIso8601String(),
      'level': level.name,
      'category': category,
      'message': message,
      'metadata': metadata,
    };
    
    // Log to console in debug mode
    if (kDebugMode) {
      print('${logEntry['level'].toUpperCase()}: ${logEntry['message']}');
    }
    
    // Send to monitoring service in production
    if (kReleaseMode) {
      // Send to Sentry, Firebase, etc.
    }
  }
}
```

#### 2. Error Reporting
```dart
class ErrorReporter {
  static Future<void> reportError(
    Object error,
    StackTrace stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Log locally
      AppLogger.log(
        'Error: ${error.toString()}',
        level: LogLevel.error,
        category: 'error_reporting',
        metadata: {
          'context': context,
          'stackTrace': stackTrace.toString(),
          ...?metadata,
        },
      );

      // Send to monitoring service
      if (kReleaseMode) {
        await Sentry.captureException(
          error,
          stackTrace: stackTrace,
          hint: context,
          extras: metadata,
        );
      }
    } catch (e) {
      // Fallback logging
      print('Error reporting failed: $e');
    }
  }
}
```

### User-Friendly Error Messages

#### 1. Error Message Mapping
```dart
class ErrorMessageMapper {
  static String getUserFriendlyMessage(Failure failure) {
    return failure.when(
      server: (message) => 'Server error occurred. Please try again later.',
      cache: (message) => 'Local data error. Please restart the app.',
      network: (message) => 'Network connection error. Please check your internet connection.',
      invalidInput: (message) => 'Invalid input. Please check your data and try again.',
      database: (message) => 'Database error. Please restart the app.',
      businessLogic: (message) => 'Application error. Please try again.',
      notFound: (message) => 'Data not found. Please refresh and try again.',
      unexpected: (message) => 'An unexpected error occurred. Please try again.',
    );
  }
}
```

#### 2. Error Recovery Strategies
```dart
class ErrorRecovery {
  static Future<void> handleNetworkError(BuildContext context) async {
    // Show retry dialog
    final shouldRetry = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Error'),
        content: const Text('Unable to connect to the server. Would you like to retry?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Retry'),
          ),
        ],
      ),
    );

    if (shouldRetry == true) {
      // Retry the operation
      await retryOperation();
    }
  }
}
```

---

## Performance Optimization

### Database Query Optimization

#### 1. Indexing Strategy
```sql
-- Create indexes for frequently queried columns
CREATE INDEX idx_income_date ON income(date);
CREATE INDEX idx_income_sync_status ON income(sync_status);
CREATE INDEX idx_orders_date ON orders(date);
CREATE INDEX idx_performance_date ON performance(date);
```

#### 2. Query Optimization
```dart
class OptimizedIncomeRepository {
  // Use pagination for large datasets
  Future<List<Income>> getIncomeWithPagination({
    required int page,
    required int pageSize,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final query = database.select(database.income)
      ..limit(pageSize)
      ..offset(page * pageSize);
    
    if (startDate != null) {
      query.where((tbl) => tbl.date.isBiggerOrEqualValue(startDate));
    }
    
    if (endDate != null) {
      query.where((tbl) => tbl.date.isSmallerOrEqualValue(endDate));
    }
    
    return await query.get();
  }
}
```

### Memory Management

#### 1. Widget Disposal
```dart
class OptimizedWidget extends StatefulWidget {
  @override
  _OptimizedWidgetState createState() => _OptimizedWidgetState();
}

class _OptimizedWidgetState extends State<OptimizedWidget> {
  late StreamSubscription _subscription;
  late Timer _timer;

  @override
  void initState() {
    super.initState();
    _subscription = someStream.listen(_handleData);
    _timer = Timer.periodic(Duration(seconds: 30), _updateData);
  }

  @override
  void dispose() {
    _subscription.cancel();
    _timer.cancel();
    super.dispose();
  }
}
```

#### 2. Image Optimization
```dart
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  
  @override
  Widget build(BuildContext context) {
    return Image.network(
      imageUrl,
      cacheWidth: 300, // Limit memory usage
      cacheHeight: 300,
      fit: BoxFit.cover,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return CircularProgressIndicator();
      },
      errorBuilder: (context, error, stackTrace) {
        return Icon(Icons.error);
      },
    );
  }
}
```

### UI Performance Best Practices

#### 1. ListView Optimization
```dart
class OptimizedListView extends StatelessWidget {
  final List<Income> incomes;
  
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: incomes.length,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          child: IncomeCard(income: incomes[index]),
        );
      },
    );
  }
}
```

#### 2. Const Widgets
```dart
class OptimizedWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: const [
        SizedBox(height: 16), // Use const for static widgets
        Text('Static Text'),
        Icon(Icons.star),
      ],
    );
  }
}
```

### Network Optimization

#### 1. Request Caching
```dart
class CachedNetworkService {
  final Map<String, CachedResponse> _cache = {};
  
  Future<Response> get(String url) async {
    final cacheKey = _generateCacheKey(url);
    
    // Check cache first
    if (_cache.containsKey(cacheKey)) {
      final cached = _cache[cacheKey]!;
      if (!cached.isExpired) {
        return cached.response;
      }
    }
    
    // Make network request
    final response = await http.get(Uri.parse(url));
    
    // Cache response
    _cache[cacheKey] = CachedResponse(
      response: response,
      timestamp: DateTime.now(),
      ttl: Duration(minutes: 5),
    );
    
    return response;
  }
}
```

#### 2. Batch Operations
```dart
class BatchSyncService {
  final List<SyncOperation> _pendingOperations = [];
  Timer? _batchTimer;
  
  void queueOperation(SyncOperation operation) {
    _pendingOperations.add(operation);
    
    // Start batch timer if not already running
    _batchTimer ??= Timer(Duration(seconds: 5), _processBatch);
  }
  
  Future<void> _processBatch() async {
    if (_pendingOperations.isEmpty) return;
    
    final operations = List<SyncOperation>.from(_pendingOperations);
    _pendingOperations.clear();
    _batchTimer?.cancel();
    _batchTimer = null;
    
    // Process all operations in a single request
    await _syncBatch(operations);
  }
}
```

---

## Security Considerations

### Data Encryption

#### 1. Local Storage Encryption
```dart
class SecureStorage {
  static const _algorithm = 'AES-256-GCM';
  
  static Future<void> storeSecurely(String key, String value) async {
    final secretKey = await _getSecretKey();
    final encrypted = await _encrypt(value, secretKey);
    await _storage.write(key: key, value: encrypted);
  }
  
  static Future<String?> retrieveSecurely(String key) async {
    final encrypted = await _storage.read(key: key);
    if (encrypted == null) return null;
    
    final secretKey = await _getSecretKey();
    return await _decrypt(encrypted, secretKey);
  }
}
```

#### 2. Database Encryption
```dart
class EncryptedDatabase extends AppDatabase {
  EncryptedDatabase(String path) : super(path);
  
  @override
  Future<void> _onCreate(Database db, int version) async {
    // Enable database encryption
    await db.execute('PRAGMA key = "your_encryption_key"');
    await super._onCreate(db, version);
  }
}
```

### Authentication Security

#### 1. Token Management
```dart
class SecureTokenManager {
  static const _tokenKey = 'auth_token';
  static const _refreshTokenKey = 'refresh_token';
  
  static Future<void> storeTokens({
    required String accessToken,
    required String refreshToken,
  }) async {
    await SecureStorage.storeSecurely(_tokenKey, accessToken);
    await SecureStorage.storeSecurely(_refreshTokenKey, refreshToken);
  }
  
  static Future<String?> getAccessToken() async {
    return await SecureStorage.retrieveSecurely(_tokenKey);
  }
  
  static Future<void> clearTokens() async {
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _refreshTokenKey);
  }
}
```

#### 2. Session Management
```dart
class SecureSessionManager {
  static const _sessionTimeout = Duration(hours: 24);
  
  static Future<bool> isSessionValid() async {
    final lastActivity = await _getLastActivity();
    if (lastActivity == null) return false;
    
    final timeSinceLastActivity = DateTime.now().difference(lastActivity);
    return timeSinceLastActivity < _sessionTimeout;
  }
  
  static Future<void> updateLastActivity() async {
    await _storage.write(
      key: 'last_activity',
      value: DateTime.now().millisecondsSinceEpoch.toString(),
    );
  }
}
```

### API Security

#### 1. Request Signing
```dart
class SecureApiClient {
  static Future<Map<String, String>> _getSecureHeaders() async {
    final token = await SecureTokenManager.getAccessToken();
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
      'X-Request-ID': _generateRequestId(),
      'X-Timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
    };
  }
  
  static String _generateRequestId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }
}
```

#### 2. Certificate Pinning
```dart
class SecureHttpClient {
  static HttpClient createSecureClient() {
    final client = HttpClient();
    
    client.badCertificateCallback = (cert, host, port) {
      // Implement certificate pinning logic
      return _isValidCertificate(cert, host);
    };
    
    return client;
  }
  
  static bool _isValidCertificate(X509Certificate cert, String host) {
    // Implement certificate validation
    return cert.pem == _expectedCertificatePem;
  }
}
```

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Database Issues

**Problem: Database corruption**
```dart
// Solution: Database recovery
class DatabaseRecovery {
  static Future<bool> attemptRecovery() async {
    try {
      // Try to repair database
      await _repairDatabase();
      return true;
    } catch (e) {
      // If repair fails, restore from backup
      return await _restoreFromBackup();
    }
  }
}
```

**Problem: Migration failures**
```dart
// Solution: Manual migration
class ManualMigration {
  static Future<void> migrateManually(int fromVersion, int toVersion) async {
    for (int version = fromVersion + 1; version <= toVersion; version++) {
      await _applyMigration(version);
    }
  }
}
```

#### 2. Sync Issues

**Problem: Sync conflicts**
```dart
// Solution: Conflict resolution
class ConflictResolver {
  static Future<void> resolveConflict(Conflict conflict) async {
    switch (conflict.type) {
      case ConflictType.localNewer:
        await _uploadLocalVersion(conflict);
        break;
      case ConflictType.remoteNewer:
        await _downloadRemoteVersion(conflict);
        break;
      case ConflictType.manual:
        await _showConflictDialog(conflict);
        break;
    }
  }
}
```

**Problem: Network connectivity**
```dart
// Solution: Connectivity monitoring
class ConnectivityMonitor {
  static Future<bool> checkConnectivity() async {
    try {
      final result = await Connectivity().checkConnectivity();
      return result != ConnectivityResult.none;
    } catch (e) {
      return false;
    }
  }
}
```

#### 3. Performance Issues

**Problem: Slow UI rendering**
```dart
// Solution: Performance profiling
class PerformanceProfiler {
  static void profileWidget(Widget widget, String name) {
    if (kDebugMode) {
      final stopwatch = Stopwatch()..start();
      
      // Wrap widget in performance overlay
      PerformanceOverlay(
        child: widget,
        onFrame: (frame) {
          if (frame.duration.inMilliseconds > 16) {
            print('Slow frame detected in $name: ${frame.duration}ms');
          }
        },
      );
    }
  }
}
```

**Problem: Memory leaks**
```dart
// Solution: Memory leak detection
class MemoryLeakDetector {
  static final Map<String, int> _widgetCounts = {};
  
  static void trackWidget(String widgetName) {
    _widgetCounts[widgetName] = (_widgetCounts[widgetName] ?? 0) + 1;
    
    if (_widgetCounts[widgetName]! > 100) {
      print('Potential memory leak detected: $widgetName');
    }
  }
}
```

### Debugging Strategies

#### 1. Logging Levels
```dart
enum LogLevel { debug, info, warning, error, critical }

class DebugLogger {
  static void log(String message, LogLevel level) {
    if (kDebugMode || level.index >= LogLevel.warning.index) {
      print('${level.name.toUpperCase()}: $message');
    }
  }
}
```

#### 2. Debug Tools
```dart
class DebugTools {
  static void showDebugMenu(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Debug Tools'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Clear Database'),
              onTap: () => _clearDatabase(),
            ),
            ListTile(
              title: const Text('Test Sync'),
              onTap: () => _testSync(),
            ),
            ListTile(
              title: const Text('Show Logs'),
              onTap: () => _showLogs(),
            ),
          ],
        ),
      ),
    );
  }
}
```

### Performance Troubleshooting

#### 1. Performance Monitoring
```dart
class PerformanceMonitor {
  static final Map<String, List<Duration>> _measurements = {};
  
  static void startMeasurement(String name) {
    _measurements[name] = [DateTime.now()];
  }
  
  static void endMeasurement(String name) {
    if (_measurements.containsKey(name)) {
      final start = _measurements[name]!.first;
      final duration = DateTime.now().difference(start);
      
      print('$name took: ${duration.inMilliseconds}ms');
      
      // Alert if performance is poor
      if (duration.inMilliseconds > 1000) {
        print('WARNING: $name is slow (${duration.inMilliseconds}ms)');
      }
    }
  }
}
```

#### 2. Memory Profiling
```dart
class MemoryProfiler {
  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      final memoryInfo = ProcessInfo.currentRss;
      print('Memory usage at $context: ${memoryInfo ~/ 1024}KB');
      
      if (memoryInfo > 100 * 1024 * 1024) { // 100MB
        print('WARNING: High memory usage detected');
      }
    }
  }
}
```

### Sync Troubleshooting

#### 1. Sync Status Monitoring
```dart
class SyncStatusMonitor {
  static Future<SyncStatus> getSyncStatus() async {
    try {
      final lastSync = await _getLastSyncTime();
      final pendingChanges = await _getPendingChangesCount();
      final connectivity = await Connectivity().checkConnectivity();
      
      return SyncStatus(
        lastSync: lastSync,
        pendingChanges: pendingChanges,
        isConnected: connectivity != ConnectivityResult.none,
        isAuthenticated: await _isAuthenticated(),
      );
    } catch (e) {
      return SyncStatus.error(e.toString());
    }
  }
}
```

#### 2. Sync Diagnostics
```dart
class SyncDiagnostics {
  static Future<SyncDiagnosticReport> runDiagnostics() async {
    final report = SyncDiagnosticReport();
    
    // Check connectivity
    report.connectivityStatus = await _testConnectivity();
    
    // Check authentication
    report.authStatus = await _testAuthentication();
    
    // Check database
    report.databaseStatus = await _testDatabase();
    
    // Check Supabase connection
    report.supabaseStatus = await _testSupabaseConnection();
    
    return report;
  }
}
```

---

## Code Quality & Standards

### Code Formatting

#### 1. Dart Formatting Rules
```yaml
# analysis_options.yaml
include: package:flutter_lints/flutter.yaml

linter:
  rules:
    - camel_case_types
    - camel_case_extensions
    - library_names
    - file_names
    - prefer_const_constructors
    - prefer_final_fields
    - avoid_print
    - avoid_empty_else
    - avoid_relative_lib_imports
```

#### 2. Import Organization
```dart
// Standard import order
import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../core/errors/failures.dart';
import '../domain/entities/income.dart';
```

### Naming Conventions

#### 1. File Naming
```
# Feature-based naming
lib/features/income/
  ├── data/
  │   ├── models/
  │   │   └── income_model.dart
  │   └── repositories/
  │       └── income_repository_impl.dart
  ├── domain/
  │   ├── entities/
  │   │   └── income.dart
  │   └── repositories/
  │       └── income_repository.dart
  └── presentation/
      ├── providers/
      │   └── income_provider.dart
      └── screens/
          └── income_screen.dart
```

#### 2. Class Naming
```dart
// Use descriptive names
class IncomeCalculationService {} // Good
class CalcService {} // Bad

// Use consistent suffixes
class IncomeRepository {} // Good
class IncomeRepo {} // Bad

// Use clear abbreviations
class AppDatabase {} // Good
class AppDB {} // Bad
```

### Documentation Standards

#### 1. Code Documentation
```dart
/// A service that handles income calculations and business logic.
///
/// This service provides methods for calculating various income-related
/// metrics such as net income, mileage, and performance indicators.
/// It follows the Single Responsibility Principle by focusing solely
/// on income-related calculations.
class IncomeCalculationService {
  /// Calculates the net income based on final and initial amounts.
  ///
  /// The net income is calculated as the difference between the final
  /// result and the initial capital. This represents the actual
  /// earnings for the period.
  ///
  /// Parameters:
  /// - [finalResult]: The total amount at the end of the period
  /// - [initialCapital]: The starting amount at the beginning
  ///
  /// Returns the calculated net income as a double.
  ///
  /// Throws [CalculationException] if the calculation fails.
  double calculateNetIncome({
    required double finalResult,
    required double initialCapital,
  }) {
    // Implementation
  }
}
```

#### 2. API Documentation
```dart
/// Repository interface for income data operations.
///
/// This interface defines the contract for income data access,
/// providing methods for CRUD operations and business logic
/// specific to income management.
abstract class IncomeRepository {
  /// Retrieves all income records from the data source.
  ///
  /// Returns a [Future] that completes with either:
  /// - A [List] of [Income] entities on success
  /// - A [Failure] on error
  ///
  /// The returned list is sorted by date in descending order.
  Future<Either<Failure, List<Income>>> getAllIncome();

  /// Saves an income record to the data source.
  ///
  /// Parameters:
  /// - [income]: The income entity to save
  ///
  /// Returns a [Future] that completes with either:
  /// - The saved [Income] entity with generated ID
  /// - A [Failure] on error
  Future<Either<Failure, Income>> saveIncome(Income income);
}
```

### Review Processes

#### 1. Code Review Checklist
```markdown
## Code Review Checklist

### Functionality
- [ ] Does the code implement the required feature?
- [ ] Are all edge cases handled?
- [ ] Are error conditions properly managed?

### Code Quality
- [ ] Is the code readable and well-structured?
- [ ] Are naming conventions followed?
- [ ] Is there appropriate documentation?

### Testing
- [ ] Are unit tests included?
- [ ] Are edge cases tested?
- [ ] Is test coverage adequate?

### Performance
- [ ] Are there any performance issues?
- [ ] Is memory usage optimized?
- [ ] Are database queries efficient?

### Security
- [ ] Are there any security vulnerabilities?
- [ ] Is sensitive data properly handled?
- [ ] Are authentication checks in place?
```

#### 2. Automated Quality Checks
```yaml
# .github/workflows/code_quality.yml
name: Code Quality

on: [push, pull_request]

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
      
      - name: Analyze code
        run: flutter analyze
        
      - name: Run tests
        run: flutter test
        
      - name: Check formatting
        run: dart format --set-exit-if-changed .
        
      - name: Generate coverage
        run: flutter test --coverage
```

---

## Future Roadmap

### Planned Features

#### 1. Advanced Analytics
- **Machine Learning Integration**: Predictive analytics for income trends
- **Performance Insights**: AI-powered recommendations for improvement
- **Comparative Analysis**: Benchmark against other drivers
- **Goal Setting**: Smart goal recommendations based on historical data

#### 2. Enhanced Sync Features
- **Real-time Sync**: WebSocket-based real-time synchronization
- **Conflict Resolution**: Advanced conflict resolution with user choice
- **Offline Queue**: Improved offline operation with better queue management
- **Multi-device Sync**: Support for multiple devices per user

#### 3. Social Features
- **Driver Community**: Connect with other drivers
- **Leaderboards**: Performance-based rankings
- **Achievement System**: Gamified achievements and badges
- **Sharing**: Share performance metrics and achievements

### Technical Debt

#### 1. Code Refactoring
- **Repository Pattern**: Standardize all repositories
- **Error Handling**: Implement consistent error handling across all features
- **State Management**: Optimize Riverpod usage and reduce provider complexity
- **Testing**: Increase test coverage to 80%+

#### 2. Performance Improvements
- **Database Optimization**: Implement database indexing and query optimization
- **Memory Management**: Reduce memory usage and prevent leaks
- **UI Performance**: Optimize widget rebuilds and rendering
- **Network Efficiency**: Implement request caching and compression

#### 3. Architecture Enhancements
- **Clean Architecture**: Ensure strict adherence to clean architecture principles
- **Dependency Injection**: Implement proper DI container
- **Modularization**: Break down large features into smaller modules
- **API Design**: Standardize API interfaces and responses

### Scalability Considerations

#### 1. Database Scalability
- **Sharding**: Implement database sharding for large datasets
- **Caching**: Add Redis or similar caching layer
- **Backup Strategy**: Implement automated backup and recovery
- **Migration Strategy**: Plan for database schema evolution

#### 2. Infrastructure Scaling
- **Load Balancing**: Implement load balancing for API endpoints
- **CDN**: Add content delivery network for static assets
- **Monitoring**: Implement comprehensive monitoring and alerting
- **Auto-scaling**: Design for automatic scaling based on demand

#### 3. User Base Scaling
- **Multi-tenancy**: Support multiple organizations
- **User Management**: Implement role-based access control
- **Data Isolation**: Ensure proper data separation between users
- **Rate Limiting**: Implement API rate limiting

### Technology Upgrades

#### 1. Framework Updates
- **Flutter Version**: Keep Flutter updated to latest stable version
- **Dependencies**: Regular dependency updates and security patches
- **Platform Support**: Add support for new platforms (web, desktop)
- **Performance**: Leverage new Flutter performance features

#### 2. Backend Evolution
- **Microservices**: Consider breaking down into microservices
- **GraphQL**: Implement GraphQL for more flexible API
- **Real-time**: Add WebSocket support for real-time features
- **AI/ML**: Integrate machine learning capabilities

#### 3. Development Tools
- **CI/CD**: Enhance automated testing and deployment
- **Monitoring**: Implement application performance monitoring
- **Analytics**: Add user behavior analytics
- **Crash Reporting**: Implement comprehensive crash reporting

### Timeline

#### Phase 1 (Q1 2024)
- Complete current feature set
- Implement comprehensive testing
- Performance optimization
- Security hardening

#### Phase 2 (Q2 2024)
- Advanced analytics features
- Enhanced sync capabilities
- Social features
- Performance monitoring

#### Phase 3 (Q3 2024)
- Machine learning integration
- Real-time features
- Multi-platform support
- Enterprise features

#### Phase 4 (Q4 2024)
- AI-powered insights
- Advanced reporting
- API for third-party integrations
- Internationalization

---

## Conclusion

The bidtrakr Driver Performance Tracker is a comprehensive Flutter application that provides drivers with powerful tools for tracking their performance, income, and vehicle maintenance. The application features a robust architecture, secure cloud synchronization, and an intuitive user interface designed for optimal user experience.

### Key Strengths
- **Clean Architecture**: Well-structured codebase following best practices
- **Offline-First**: Full functionality without internet connection
- **Secure Sync**: Reliable cloud synchronization with conflict resolution
- **User-Friendly**: Intuitive interface with responsive design
- **Scalable**: Modular architecture for easy feature additions
- **Maintainable**: Comprehensive documentation and testing

### Future Enhancements
- **Real-time Notifications**: Push notifications for important events
- **Advanced Analytics**: Machine learning-powered insights
- **Multi-language Support**: Internationalization for global users
- **API Integration**: Third-party service integrations
- **Advanced Reporting**: Custom report generation

The application demonstrates modern Flutter development practices and provides a solid foundation for future enhancements and scalability. 

### UI/UX Details: Income Screen

The Income screen is designed for clarity, efficiency, and a modern Material 3 look, leveraging custom widgets and advanced Flutter layout patterns. Below is a detailed breakdown of its UI/UX:

#### Layout Structure
- **Scaffold** with a `CustomScrollView` using slivers for flexible, performant scrolling.
- **SliverAppBar** (floating, pinned):
  - Title: "Income"
  - **Date Range Selector**: Appears in the AppBar's bottom area, supports shimmer loading and error states.
  - **Add Button**: IconButton in the AppBar to add a new income record (navigates to the form screen).

#### Main Content (Slivers)
1. **Summary Section** (`_buildSummarySection`):
   - Title: "Summary"
   - Subtitle: "Overview of your income and forecasts"
   - **IncomeSummary** widget: Shows total net income, record count, highest income, etc.
   - **MileageSummary** widget: Shows total mileage and record count.
   - **IncomeTrendsCard**: Visualizes income trends (with loading and error states).
2. **History Header**: `IncomeHistoryHeader` widget for section labeling.
3. **Paginated Income List**:
   - List of `IncomeItemCard` widgets, each representing an income record.
   - **Infinite Scroll**: Loads more data as the user scrolls near the end (via ScrollController).
   - **Loading Indicator**: Shown at the end if more pages exist.
   - **Empty State**: Card with icon, message, and "Add Income Record" button if no records exist.
   - **Pull-to-Refresh**: Entire list is wrapped in a `RefreshIndicator` for manual refresh.

#### Interaction Patterns
- **Add Record**: Tap the add button in the AppBar or the empty state button to open the Income Form.
- **View Details**: Tap an `IncomeItemCard` to open a bottom sheet (`IncomeDetailsSheet`) with full record details.
- **Edit/Delete**: Long-press an `IncomeItemCard` to open an actions bottom sheet (edit or delete). Deletion is confirmed via a dialog.
- **Infinite Scroll**: More records are loaded automatically as the user scrolls.

#### Error, Loading, and Empty States
- **Loading**: Uses `IncomeUnifiedShimmerLoading` for skeleton loading of the entire screen.
- **Error**: Error containers with icons and messages for failed loads (date range, trends, or list).
- **Empty State**: Card with icon, message, and action button, plus a "Pull down to refresh" hint.

#### Theming & Accessibility
- **Colors**: Uses `AppColors` for primary, success, and text colors. Cards and backgrounds use Material 3 color system.
- **Typography**: All text uses theme styles for consistency and accessibility.
- **Touch Targets**: All buttons and cards meet minimum size requirements.
- **Tooltips**: Icon buttons have tooltips for screen readers.
- **Contrast**: Ensures readable contrast for all text and icons.
- **Rounded Corners**: Cards and buttons use consistent border radii.

#### Income Form Screen (Add/Edit)
- **BaseFormScreen**: Custom base class for form screens.
- **Sections**:
  - **Mileage Information**: `MileageInput` widgets for initial (and, if editing, final) mileage, with validation.
  - **Initial Balance**: `CurrencyInputField` widgets for each payment method (GoPay, BCA, Cash, OVO, BRI, Rekpon).
  - **Final Balance**: Only shown when editing; same fields as initial balance.
- **Validation**:
  - Mileage and balances must be valid numbers; final mileage cannot be less than initial.
  - Date uniqueness is checked before submission.
- **Feedback**: Success and error snackbars on submit; error messages for validation failures.
- **Section Titles**: Each section has a title and icon for clarity.

#### Example Widget Hierarchy (Simplified)
```dart
Scaffold(
  body: RefreshIndicator(
    onRefresh: ...,
    child: CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverAppBar(...),
        SliverToBoxAdapter(child: SummarySection(...)),
        SliverToBoxAdapter(child: IncomeHistoryHeader()),
        SliverList(delegate: ... IncomeItemCard ...),
      ],
    ),
  ),
)
```

#### Custom Widgets Used
- `IncomeSummary`, `MileageSummary`, `IncomeTrendsCard`, `IncomeItemCard`, `IncomeDetailsSheet`, `IncomeHistoryHeader`, `DateRangeSelectorField`, `ItemActionsBottomSheet`, `IncomeUnifiedShimmerLoading`, `CurrencyInputField`, `MileageInput`, `BaseFormScreen`.

#### Best Practices
- Use slivers for performance and flexibility with large lists.
- Provide clear, actionable empty and error states.
- Use custom widgets for modularity and reusability.
- Ensure all interactive elements are accessible and have tooltips/labels.
- Validate all user input and provide immediate feedback.

---

### UI/UX Details: Orders Screen

The Orders screen is designed for efficient order management and performance tracking, using a modern Material 3 look and custom widgets.

#### Layout Structure
- **Scaffold** with a `CustomScrollView` using slivers.
- **SliverAppBar** (floating, pinned):
  - Title: "Orders"
  - **Date Range Selector**: In the AppBar's bottom area, with shimmer loading and error states.
  - **Add Button**: IconButton in the AppBar to add a new order record (navigates to the order form).

#### Main Content (Slivers)
1. **Summary Section**: Displays order metrics and statistics using custom widgets (e.g., `OrderMetrics`).
2. **History Header**: `OrderHistoryHeader` widget for section labeling.
3. **Paginated Order List**:
   - List of `OrderListItem` widgets, each representing an order record.
   - **Infinite Scroll**: Loads more data as the user scrolls.
   - **Loading Indicator**: At the end if more pages exist.
   - **Empty State**: Card with icon, message, and "Add Order Record" button if no records exist.
   - **Pull-to-Refresh**: Via `RefreshIndicator`.

#### Interaction Patterns
- **Add Record**: Tap the add button in the AppBar or the empty state button to open the Order Form.
- **View Details**: Tap an `OrderListItem` to open a bottom sheet (`OrderDetailsBottomSheet`) with full record details.
- **Edit/Delete**: Long-press an `OrderListItem` to open an actions bottom sheet (edit or delete). Deletion is confirmed via a dialog.
- **Infinite Scroll**: More records are loaded automatically as the user scrolls.

#### Error, Loading, and Empty States
- **Loading**: Uses `OrderStandardizedShimmerLoading` for skeleton loading.
- **Error**: Error containers for failed loads.
- **Empty State**: Card with icon, message, and action button.

#### Theming & Accessibility
- **Colors**: Uses `AppColors` and Material 3 color system.
- **Typography**: Theme styles for all text.
- **Touch Targets**: All buttons and cards meet minimum size requirements.
- **Tooltips**: Icon buttons have tooltips.
- **Contrast**: Ensures readable contrast.
- **Rounded Corners**: Consistent border radii.

#### Custom Widgets Used
- `OrderMetrics`, `OrderListItem`, `OrderDetailsBottomSheet`, `OrderHistoryHeader`, `DateRangeSelectorField`, `ItemActionsBottomSheet`, `OrderStandardizedShimmerLoading`.

#### Best Practices
- Use slivers for performance.
- Provide clear empty and error states.
- Modularize with custom widgets.
- Ensure accessibility and validation.

---

### UI/UX Details: Performance Screen

The Performance screen focuses on analytics and trends, using a clean, data-rich layout.

#### Layout Structure
- **Scaffold** with a `CustomScrollView` using slivers.
- **SliverAppBar** (floating, pinned):
  - Title: "Performance"
  - **Date Range Selector**: In the AppBar's bottom area.

#### Main Content (Slivers)
1. **Header Section**: Displays performance metrics and charts using custom widgets (e.g., `PerformanceMetricsCard`).
2. **Paginated Performance List**:
   - List of performance records, each as a custom widget (e.g., `PerformanceListItem`).
   - **Infinite Scroll**: Loads more data as the user scrolls.
   - **Loading Indicator**: At the end if more pages exist.
   - **Empty State**: Card with icon, message, and action button.
   - **Pull-to-Refresh**: Via `RefreshIndicator`.

#### Interaction Patterns
- **View Details**: Tap a performance record to see more details (if supported).
- **Infinite Scroll**: More records are loaded automatically as the user scrolls.

#### Error, Loading, and Empty States
- **Loading**: Uses `PerformanceUnifiedShimmerLoading` for skeleton loading.
- **Error**: Error containers for failed loads.
- **Empty State**: Card with icon, message, and action button.

#### Theming & Accessibility
- **Colors**: Uses `AppColors` and Material 3 color system.
- **Typography**: Theme styles for all text.
- **Touch Targets**: All buttons and cards meet minimum size requirements.
- **Contrast**: Ensures readable contrast.
- **Rounded Corners**: Consistent border radii.

#### Custom Widgets Used
- `PerformanceMetricsCard`, `PerformanceListItem`, `PerformanceUnifiedShimmerLoading`, `DateRangeSelectorField`.

#### Best Practices
- Use slivers for performance.
- Provide clear empty and error states.
- Modularize with custom widgets.
- Ensure accessibility and validation.

---

### UI/UX Details: More Screen

The More screen provides access to additional features, settings, and support.

#### Layout Structure
- **Scaffold** with an AppBar and a `ListView`.
- **AppBar**: Title ("More Options") and optional actions (e.g., sync status).
- **Main Content**: ListView with:
  - `UserInfoCard` at the top.
  - **Features Section**: `MoreSectionHeader` and a card with feature menu items.
  - **Settings Section**: `MoreSectionHeader` and a card with settings menu items.
  - **Footer**: `AppFooter` widget.

#### Interaction Patterns
- **Navigate to Features**: Tap menu items to navigate to feature screens (e.g., Backup, Spare Parts, Level, Settings).
- **Sync Status**: Mini sync status indicator in the AppBar.

#### Error, Loading, and Empty States
- **Loading**: Loading indicators for async data (e.g., user info, sync status).
- **Error**: Error cards for failed loads (e.g., settings, backup directory).
- **Empty State**: Not typically present, as the More screen always has static content.

#### Theming & Accessibility
- **Colors**: Uses `AppColors` and Material 3 color system.
- **Typography**: Theme styles for all text.
- **Touch Targets**: All buttons and cards meet minimum size requirements.
- **Contrast**: Ensures readable contrast.
- **Rounded Corners**: Consistent border radii.

#### Custom Widgets Used
- `UserInfoCard`, `MoreSectionHeader`, `MenuCard`, `MenuItem`, `AppFooter`, `SyncStatusWidget`, `SettingsSectionHeader`, `AppInfoCard`, `BackupDirectorySettingsCard`, `DateRangeSettingsCard`.

#### Best Practices
- Use ListView for vertical scrolling.
- Modularize with custom widgets.
- Ensure accessibility and validation.
- Provide clear navigation paths to all features and settings.

---

### UI/UX Details: Spare Parts Screens

The Spare Parts feature consists of several screens: the main spare parts list, add/edit form, replace part, and replacement history. Each is designed for clarity, maintenance tracking, and actionable feedback.

#### Spare Parts List Screen
- **Scaffold** with AppBar (title: "Spare Parts", add button for new part).
- **Body**: Async state (loading, error, or data):
  - **Loading**: Centered CircularProgressIndicator.
  - **Error**: `SparePartsErrorView` with retry.
  - **Empty**: `SparePartsEmptyView` with add and refresh actions.
  - **Data**: `CustomScrollView` with slivers:
    - **MaintenanceAlertCard**: If any parts have warning status (e.g., near replacement), shown at the top.
    - **SliverList**: List of `SparePartCard` widgets, grouped by warning status, sorted by usage percent.
    - **Pull-to-Refresh**: Entire list wrapped in `RefreshIndicator`.
- **Interaction Patterns**:
  - **Add**: AppBar add button or empty state button opens `SparePartFormScreen`.
  - **View/Actions**: Tap a `SparePartCard` to open `SparePartsOptionsBottomSheet` (view history, replace, edit, delete).
  - **Delete**: Confirmed via dialog, with success/error snackbar.
  - **Replace**: Navigates to `ReplaceSparePartScreen`.
  - **View History**: Navigates to `EnhancedReplacementHistoryScreen`.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, tooltips, large touch targets, and clear warning indicators.
- **Custom Widgets**: `SparePartCard`, `MaintenanceAlertCard`, `SparePartsOptionsBottomSheet`, `SparePartsEmptyView`, `SparePartsErrorView`.

#### Add/Edit Spare Part Screen
- **Scaffold** with AppBar (title: "Add/Edit Spare Part", save button).
- **Body**: SingleChildScrollView with Form:
  - **PartInformationSection**: Name, type, price fields.
  - **MileageInformationSection**: Mileage limit, initial mileage, installation date, use current mileage toggle.
  - **AdditionalInformationSection**: Notes.
  - **Validation**: All fields validated; error messages shown inline.
  - **Loading**: Centered CircularProgressIndicator when loading data.
  - **Feedback**: Success/error snackbars on save.
- **Theming & Accessibility**: Consistent with app, clear section headers, icons, and accessible form fields.
- **Custom Widgets**: `PartInformationSection`, `MileageInformationSection`, `AdditionalInformationSection`, `SectionHeader`.

#### Replace Spare Part Screen
- **Scaffold** with AppBar (title: "Replace Spare Part", save button).
- **Body**: SingleChildScrollView with Form:
  - **PartInfoSection**: Name, type, price, with toggles to keep same values or edit.
  - **MileageInfoSection**: Mileage limit, mileage at replacement, current mileage (read-only), with toggles.
  - **ReplacementInfoSection**: Replacement date (date picker), reason, notes.
  - **Validation**: All fields validated; error messages shown inline.
  - **Loading**: Centered CircularProgressIndicator when loading data.
  - **Feedback**: Success/error snackbars on save.
- **Theming & Accessibility**: Consistent with app, clear section headers, icons, and accessible form fields.
- **Custom Widgets**: `_PartInfoSection`, `_MileageInfoSection`, `_ReplacementInfoSection`, `CustomDatePicker`, `CustomTextField`.

#### Replacement History Screen
- **Scaffold** with AppBar (title: "Replacement History").
- **Body**: Async state (loading, error, or data):
  - **Loading**: Centered CircularProgressIndicator.
  - **Error**: `HistoryErrorWidget` with retry.
  - **Empty**: `EmptyHistoryWidget` with helpful message.
  - **Data**: `HistoryListWidget` showing chronological list of replacements, each as a card with details (date, mileage, reason, notes, etc.).
- **Interaction Patterns**:
  - **View Details**: Expandable cards for more info.
  - **Refresh**: Pull-to-refresh or retry on error.
- **Theming & Accessibility**: Consistent with app, clear visual indicators for most recent replacement, accessible cards.
- **Custom Widgets**: `HistoryListWidget`, `EmptyHistoryWidget`, `HistoryErrorWidget`.

#### Best Practices
- Use clear warning and status indicators for maintenance.
- Modularize with custom widgets for each section and card.
- Validate all user input and provide immediate feedback.
- Ensure all actions are accessible and have tooltips/labels.
- Provide actionable empty and error states.

---

### UI/UX Details: Backup Screens

The Backup feature provides backup and restore functionality via a tabbed interface.

#### Backup & Restore Main Screen
- **Scaffold** with AppBar (title: "Backup & Restore").
- **TabBar** in AppBar: Two tabs (Backup, Restore) with icons.
- **TabBarView**: Shows `BackupTab` and `RestoreTab`.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, large touch targets, clear icons, and accessible tab navigation.
- **Custom Widgets**: `BackupTab`, `RestoreTab`.

#### Backup Tab
- **SingleChildScrollView** with Column layout.
- **InfoCard**: Explains backup, with action button to create backup.
- **LoadingIndicator**: Shown when creating backup.
- **StatusCard**: Shows success or error after backup operation.
- **Recent Backups**: Header and list of `BackupCardItem` widgets for each backup file.
- **Empty State**: `EmptyBackupCard` with message and action button.
- **Interaction Patterns**:
  - **Create Backup**: Button triggers backup creation.
  - **Delete Backup**: Long-press a backup to open `BackupActionsBottomSheet` (delete option, confirmation dialog).
- **Theming & Accessibility**: Consistent with app, clear feedback, accessible actions.

#### Restore Tab
- **SingleChildScrollView** with Column layout.
- **InfoCard**: Explains restore, with action button to refresh backup list.
- **Warning**: Prominent warning about data replacement.
- **LoadingIndicator**: Shown when restoring.
- **StatusCard**: Shows success or error after restore operation.
- **Available Backups**: Header and list of `BackupCardItem` widgets (tap to restore, confirmation dialog).
- **Empty State**: `EmptyBackupCard` with message.
- **Interaction Patterns**:
  - **Restore Backup**: Tap a backup to restore (confirmation dialog).
  - **Refresh List**: Button to refresh available backups.
- **Theming & Accessibility**: Consistent with app, clear feedback, accessible actions.

#### Best Practices
- Use clear warnings for destructive actions.
- Modularize with custom widgets for cards, lists, and dialogs.
- Provide actionable empty and error states.
- Ensure all actions are accessible and have tooltips/labels.

---

### UI/UX Details: Settings Screen

The Settings screen allows users to configure app preferences and backup directory.

#### App Settings Screen
- **Scaffold** with AppBar (title: "App Settings").
- **Body**: Async state (loading, error, or data):
  - **Loading**: Centered CircularProgressIndicator.
  - **Error**: `SettingsErrorCard` with retry.
  - **Data**: ListView with:
    - `AppInfoCard`: App version/info.
    - `SettingsSectionHeader`: For each section (Date Range, Backup Settings, etc.).
    - `DateRangeSettingsCard`: Date range picker.
    - `BackupDirectorySettingsCard`: Directory picker.
- **Interaction Patterns**:
  - **Change Date Range**: Opens date range picker.
  - **Change Backup Directory**: Opens directory picker.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, clear section headers, accessible fields.
- **Custom Widgets**: `AppInfoCard`, `SettingsSectionHeader`, `DateRangeSettingsCard`, `BackupDirectorySettingsCard`, `SettingsErrorCard`.

#### Best Practices
- Group related settings with clear headers.
- Provide immediate feedback for changes.
- Ensure all fields and actions are accessible.
- Provide actionable error states.

---

### UI/UX Details: Levels Screens

The Levels feature provides gamified progression and settings for driver levels.

#### Levels Main Screen
- **Scaffold** with AppBar (title: "Level", settings button).
- **Body**: RefreshIndicator with SingleChildScrollView.
  - **CurrentLevelCard**: Shows current level, points, and progress.
  - **NextLevelCard/PlatinumLevelCard**: Shows requirements and progress to next level, or platinum status.
- **Interaction Patterns**:
  - **Refresh**: Pull-to-refresh updates level data.
  - **Open Settings**: AppBar button navigates to Level Settings screen.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, clear progress indicators, accessible cards.
- **Custom Widgets**: `CurrentLevelCard`, `NextLevelCard`, `PlatinumLevelCard`, `LevelLoadingWidget`, `LevelErrorWidget`.

#### Level Settings Screen
- **Scaffold** with AppBar (title: "Level Settings", save button).
- **Body**: Form in SingleChildScrollView:
  - `LevelInfoCard`: Explains level system.
  - `LevelRequirementSection`: For each level (Silver, Gold, Platinum), with fields for points, bid rate, trip completion.
  - **Validation**: All fields validated; error messages shown inline.
  - **Loading**: Centered CircularProgressIndicator when saving.
  - **Feedback**: Success/error snackbars on save.
- **Interaction Patterns**:
  - **Edit Requirements**: Change fields for each level.
  - **Save**: Save button validates and persists changes.
- **Theming & Accessibility**: Consistent with app, clear section headers, accessible fields.
- **Custom Widgets**: `LevelInfoCard`, `LevelRequirementSection`, `SettingsErrorCard`.

#### Best Practices
- Use clear progress and achievement indicators.
- Modularize with custom widgets for each card and section.
- Validate all user input and provide immediate feedback.
- Ensure all actions are accessible and have tooltips/labels.
- Provide actionable error states.

---

### UI/UX Details: Authentication Screens

#### Login Screen
- **Scaffold** with no AppBar, background color from theme.
- **Body**: SafeArea, Center, SingleChildScrollView, Form.
  - App name/logo at top.
  - Error message (if present) in colored container.
  - Email and password fields (`AuthTextField`), with validation.
  - Login button (`AuthButton`), loading indicator if submitting.
  - Links to Signup and Forgot Password screens.
- **Interaction Patterns**:
  - **Login**: Validate and submit form, show loading, error, or success.
  - **Navigate**: Tap links to go to Signup or Forgot Password.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, large touch targets, accessible fields, tooltips, and error feedback.
- **Custom Widgets**: `AuthTextField`, `AuthButton`.

#### Signup Screen
- **Scaffold** with AppBar (transparent), SafeArea, Center, SingleChildScrollView, Form.
  - Name, email, password, confirm password fields.
  - Signup button, loading indicator if submitting.
  - Error message (if present).
  - Link to Login screen.
- **Interaction Patterns**:
  - **Register**: Validate and submit form, show loading, error, or success.
  - **Navigate**: Tap link to go to Login.
- **Theming & Accessibility**: Consistent with Login screen.
- **Custom Widgets**: `AuthTextField`, `AuthButton`.

#### Forgot Password Screen
- **Scaffold** with AppBar (transparent), SafeArea, Center, SingleChildScrollView, Form.
  - Email field, reset password button.
  - Error/success message (if present).
  - Link to Login screen.
- **Interaction Patterns**:
  - **Reset Password**: Validate and submit form, show loading, error, or success.
  - **Navigate**: Tap link to go to Login.
- **Theming & Accessibility**: Consistent with Login/Signup screens.
- **Custom Widgets**: `AuthTextField`, `AuthButton`.

#### Best Practices
- Use clear error and success feedback.
- Validate all user input.
- Ensure all fields and actions are accessible.
- Provide actionable error states and navigation links.

---

### UI/UX Details: Performance Details Screen

- **Performance Details Sheet**: Modal bottom sheet (`PerformanceDetailsSheet`).
  - Shows detailed metrics for a selected performance record.
  - Uses cards, icons, and clear typography for each metric.
  - Close button or swipe down to dismiss.
- **Interaction Patterns**:
  - **Open**: Tap a performance list item to open details sheet.
  - **Dismiss**: Tap close or swipe down.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, accessible cards, large touch targets.
- **Custom Widgets**: `PerformanceDetailsSheet`.

---

### UI/UX Details: Order Details Screen

- **Order Details Sheet**: Modal bottom sheet (`OrderDetailsBottomSheet`).
  - Shows detailed info for a selected order record.
  - Uses cards, icons, and clear typography for each field.
  - Edit and delete actions (if permitted).
  - Close button or swipe down to dismiss.
- **Interaction Patterns**:
  - **Open**: Tap an order list item to open details sheet.
  - **Edit/Delete**: Tap actions in sheet.
  - **Dismiss**: Tap close or swipe down.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, accessible cards, large touch targets.
- **Custom Widgets**: `OrderDetailsBottomSheet`.

---

### UI/UX Details: Cloud Sync Screen

- **Scaffold** with AppBar (title: "Cloud Sync").
- **Body**: ListView with status, auto-sync, options, and info cards.
  - **SyncStatusIndicatorWidget**: Shows current sync state, next scheduled sync, and detailed status.
  - **Manual Sync Button**: Triggers sync operation.
  - **Auto Sync Card**: Toggle for enabling/disabling auto sync.
  - **Sync Options Card**: Additional sync settings.
  - **Info Card**: Explains sync process and best practices.
- **Interaction Patterns**:
  - **Sync Now**: Tap button to trigger manual sync.
  - **Toggle Auto Sync**: Enable/disable auto sync.
  - **View Status**: See current and next sync times.
- **Theming & Accessibility**: Uses `AppColors`, Material 3, accessible cards, large touch targets, tooltips.
- **Custom Widgets**: `SyncStatusIndicatorWidget`, `AppFooter`.

#### Best Practices
- Provide clear sync status and feedback.
- Ensure all actions are accessible and have tooltips/labels.
- Use cards and icons for clarity.

---

### UI/UX Details: Income Details Screen

The Income Details Screen provides a detailed view of a single income record, presented as a modal bottom sheet (`IncomeDetailsSheet`). This screen is designed for clarity, quick review, and actionable options.

#### Layout Structure
- **Modal Bottom Sheet** (`showModalBottomSheet`):
  - Rounded top corners, draggable, dismissible by swipe or close button.
  - Scrollable content for long records.
- **Header**:
  - Title: "Income Details"
  - Close button (top right, with tooltip and accessible label).
- **Content**:
  - **Date**: Displayed prominently at the top.
  - **Mileage Section**: Shows initial and final mileage, calculated mileage, with icons.
  - **Payment Methods**: List of all payment methods (GoPay, BCA, Cash, OVO, BRI, Rekpon), each with initial, final, and net income values, using `CurrencyDisplayRow` or similar custom widgets.
  - **Summary Section**: Net income, total capital, final result, with clear labels and icons.
  - **Notes**: If present, shown in a dedicated card or section.
  - **Timestamps**: Created/updated at, if relevant.
- **Actions**:
  - **Edit**: Button to open the income form in edit mode.
  - **Delete**: Button to delete the record (with confirmation dialog).

#### Interaction Patterns
- **Open**: Tap an `IncomeItemCard` in the income list to open the details sheet.
- **Close**: Tap close button or swipe down.
- **Edit**: Tap edit button to open the form pre-filled with this record's data.
- **Delete**: Tap delete button, confirm in dialog, see feedback via snackbar.

#### Theming & Accessibility
- **Colors**: Uses `AppColors` and Material 3 for backgrounds, cards, and icons.
- **Typography**: Theme styles for all text, with clear section headers.
- **Touch Targets**: All buttons and actions meet minimum size requirements.
- **Tooltips**: All action icons have tooltips and semantic labels.
- **Contrast**: Ensures readable contrast for all text and icons.
- **Rounded Corners**: Consistent with other modals and cards in the app.

#### Error, Loading, and Empty States
- **Loading**: Shimmer or loading indicator if data is being fetched asynchronously.
- **Error**: Error container with retry if loading fails.
- **Empty State**: Not applicable (sheet only opens for existing records).

#### Custom Widgets Used
- `IncomeDetailsSheet`, `CurrencyDisplayRow`, `MileageDisplayRow`, `SectionHeader`, `ActionButton`, `NotesCard`.

#### Best Practices
- Use a modal bottom sheet for focused, contextual details.
- Group related fields with clear section headers.
- Provide actionable edit and delete options with confirmation and feedback.
- Ensure all actions are accessible and have tooltips/labels.
- Use consistent theming and layout with other details screens.

---

### UI/UX Details: About App Dialog

The About App Dialog provides users with information about the application, including version, developer, and release year.

#### Layout Structure
- **AlertDialog**:
  - Title: "About"
  - Content: App description, info card with version, developer, release year.
  - Actions: Close button.
- **Info Card**: Card with icon, section title, and info rows.

#### Theming & Accessibility
- Uses `AppColors` and Material 3 for backgrounds, icons, and text.
- All text uses theme styles for clarity and accessibility.
- Close button is accessible and labeled.
- Info rows use clear labels and values.

#### Best Practices
- Use AlertDialog for concise, focused app info.
- Group related info in a card for clarity.
- Ensure all actions are accessible and labeled.

---

### UI/UX Details: Spare Parts Options Bottom Sheet

The Spare Parts Options Bottom Sheet provides quick actions for a selected spare part.

#### Layout Structure
- **Modal Bottom Sheet** (`showModalBottomSheet`):
  - Rounded top corners, draggable, dismissible.
  - Column layout with action buttons.
- **Actions**:
  - View Replacement History
  - Replace Part
  - Edit Part
  - Delete Part (destructive, confirmation required)
  - Cancel button
- **Action Buttons**: Each with icon, color, label, description.

#### Theming & Accessibility
- Uses `AppColors` for action icons (info, success, warning, error).
- Large touch targets for all actions.
- Destructive actions are clearly marked.
- All actions have tooltips/labels.

#### Best Practices
- Use modal bottom sheet for contextual actions.
- Group actions with clear icons and descriptions.
- Ensure destructive actions require confirmation.
- Provide accessible, labeled actions.

---

### UI/UX Details: Backup Actions Bottom Sheet

The Backup Actions Bottom Sheet provides actions for a selected backup file.

#### Layout Structure
- **Modal Bottom Sheet** (`showModalBottomSheet`):
  - Rounded top corners, draggable, dismissible.
  - Column layout with action buttons.
- **Actions**:
  - Restore Backup
  - Delete Backup (destructive, confirmation required)
  - Cancel button
- **Action Buttons**: Each with icon, color, label, description.

#### Theming & Accessibility
- Uses theme colors for action icons.
- Large touch targets for all actions.
- Destructive actions are clearly marked.
- All actions have tooltips/labels.

#### Best Practices
- Use modal bottom sheet for contextual actions.
- Group actions with clear icons and descriptions.
- Ensure destructive actions require confirmation.
- Provide accessible, labeled actions.

---

### UI/UX Details: Restore Backup Confirmation Dialog

The Restore Backup Confirmation Dialog ensures users confirm before restoring a backup, warning about data replacement.

#### Layout Structure
- **AlertDialog**:
  - Title: "Confirm Restore"
  - Content: Warning message, backup info card, additional warning row with icon.
  - Actions: Cancel and Restore buttons.

#### Theming & Accessibility
- Uses `AppColors` for warning icons and text.
- All text uses theme styles for clarity and accessibility.
- Restore button is styled as secondary/destructive.
- All actions are accessible and labeled.

#### Best Practices
- Use AlertDialog for critical confirmations.
- Provide clear, actionable warnings and info.
- Ensure all actions are accessible and labeled.

---