# bidtrakr

A new Flutter project.

## Getting Started

# bidtrakr - Driver Performance Tracker

A comprehensive Flutter application designed for driver performance tracking and income management. The app helps drivers monitor their daily earnings, order performance, vehicle maintenance, and overall productivity metrics.

## Features

- **Income Tracking**: Monitor daily earnings across multiple payment methods (GoPay, BCA, Cash, OVO, BRI, RekPon)
- **Order Management**: Track completed, missed, and canceled orders with performance metrics
- **Performance Analytics**: Analyze bid acceptance rates and trip completion metrics
- **Spare Parts Management**: Monitor vehicle maintenance and replacement schedules
- **Level System**: Gamified progression system with platinum, gold, and silver tiers
- **Cloud Sync**: Secure data synchronization with Supabase backend
- **Backup & Restore**: Local data backup and restoration capabilities

## Technology Stack

- **Framework**: Flutter 3.8.1+
- **State Management**: Riverpod
- **Database**: Drift (SQLite)
- **Backend**: Supabase
- **Code Generation**: Freezed, JSON Serialization
- **Charts**: FL Chart
- **Architecture**: Clean Architecture

## Getting Started

### Prerequisites

- Flutter SDK 3.8.1 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / VS Code
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bidtrakr
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your Supabase credentials
   ```

4. **Generate code**
   ```bash
   flutter packages pub run build_runner build --delete-conflicting-outputs
   ```

5. **Run the application**
   ```bash
   flutter run
   ```

## Project Structure

```
lib/
├── core/                    # Core application layer
│   ├── components/         # Reusable UI components
│   ├── config/            # Configuration files
│   ├── constants/         # App constants
│   ├── datasources/       # Database layer
│   ├── di/               # Dependency injection
│   ├── errors/           # Error handling
│   ├── initialization/   # App initialization
│   ├── models/          # Data models
│   ├── providers/       # State management
│   ├── repositories/    # Data repositories
│   ├── services/        # Business logic services
│   ├── theme/          # UI theming
│   ├── utils/          # Utility functions
│   └── widgets/        # Shared widgets
└── features/           # Feature modules
    ├── auth/           # Authentication
    ├── backup/         # Backup & restore
    ├── home/           # Main navigation
    ├── income/         # Income tracking
    ├── levels/         # Level system
    ├── more/           # Additional features
    ├── orders/         # Order management
    ├── performance/    # Performance analytics
    ├── settings/       # App settings
    ├── spare_parts/    # Spare parts management
    └── sync/           # Sync functionality
```

## Development

### Code Generation

Run code generation when you modify models or add new ones:

```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Testing

Run tests:

```bash
flutter test
```

Run tests with coverage:

```bash
flutter test --coverage
```

### Building

Build for Android:

```bash
flutter build apk --release
```

Build for iOS:

```bash
flutter build ios --release
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or create an issue in this repository.
