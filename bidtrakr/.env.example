# Supabase Configuration
# Copy this file to .env and fill in your actual values
# DO NOT commit the .env file to version control

# Supabase URL - Get this from your Supabase project settings
SUPABASE_URL=your_supabase_project_url_here

# Supabase Anon Key - Get this from your Supabase project API settings
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Optional: Supabase Service Role Key (for admin operations)
# SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Database Configuration
# Local database name (optional, defaults to 'bidtrakr.db')
# DATABASE_NAME=bidtrakr.db

# App Configuration
# App environment (development, staging, production)
APP_ENV=development

# Debug mode (true/false)
DEBUG_MODE=true

# Logging level (debug, info, warning, error)
LOG_LEVEL=debug
