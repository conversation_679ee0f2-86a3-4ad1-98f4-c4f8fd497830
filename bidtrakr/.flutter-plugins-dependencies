{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.36/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.50/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_darwin", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_darwin-1.5.0/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.36/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "app_links_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/", "native_build": false, "dependencies": ["gtk"], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "gtk", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "app_links", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "local_auth_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "sqlite3_flutter_libs", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqlite3_flutter_libs-0.5.36/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "app_links_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "file_picker", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.7/", "dependencies": [], "dev_dependency": false}, {"name": "flutter_secure_storage_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "app_links", "dependencies": ["app_links_linux", "app_links_web"]}, {"name": "app_links_linux", "dependencies": ["gtk"]}, {"name": "app_links_web", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_picker", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "flutter_secure_storage", "dependencies": ["flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_web", "flutter_secure_storage_windows"]}, {"name": "flutter_secure_storage_linux", "dependencies": []}, {"name": "flutter_secure_storage_macos", "dependencies": []}, {"name": "flutter_secure_storage_web", "dependencies": []}, {"name": "flutter_secure_storage_windows", "dependencies": ["path_provider"]}, {"name": "gtk", "dependencies": []}, {"name": "local_auth", "dependencies": ["local_auth_android", "local_auth_darwin", "local_auth_windows"]}, {"name": "local_auth_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "local_auth_darwin", "dependencies": []}, {"name": "local_auth_windows", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqlite3_flutter_libs", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-07-18 13:00:51.953955", "version": "3.32.5", "swift_package_manager_enabled": {"ios": false, "macos": false}}