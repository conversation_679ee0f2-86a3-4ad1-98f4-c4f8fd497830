import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../services/security_service.dart';
import '../services/biometric_service.dart';
import '../errors/exceptions.dart';

/// Security middleware for handling app security measures
class SecurityMiddleware {
  SecurityMiddleware({
    required SecurityService securityService,
    required BiometricService biometricService,
  })  : _securityService = securityService,
        _biometricService = biometricService;

  final SecurityService _securityService;
  final BiometricService _biometricService;

  static const int _maxFailedAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);
  
  int _failedAttempts = 0;
  DateTime? _lockoutUntil;
  Timer? _lockoutTimer;

  /// Initialize security middleware
  Future<void> initialize() async {
    try {
      // Set up security measures
      await _setupSecurityMeasures();
      
      if (kDebugMode) {
        print('Security middleware initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize security middleware: $e');
      }
    }
  }

  /// Set up security measures
  Future<void> _setupSecurityMeasures() async {
    // Prevent screenshots in production
    if (!kDebugMode) {
      try {
        await SystemChrome.setEnabledSystemUIMode(
          SystemUiMode.manual,
          overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
        );
      } catch (e) {
        if (kDebugMode) {
          print('Failed to set system UI mode: $e');
        }
      }
    }

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  /// Validate authentication attempt
  Future<SecurityValidationResult> validateAuthAttempt({
    required String email,
    required String password,
  }) async {
    try {
      // Check if account is locked
      if (isLockedOut()) {
        return SecurityValidationResult.lockedOut(
          'Account is temporarily locked. Try again later.',
          _lockoutUntil!,
        );
      }

      // Validate input for suspicious patterns
      if (_securityService.containsSuspiciousPatterns(email) ||
          _securityService.containsSuspiciousPatterns(password)) {
        _recordFailedAttempt();
        return SecurityValidationResult.suspicious(
          'Invalid input detected.',
        );
      }

      // Validate email format
      if (!_securityService.isValidEmail(email)) {
        _recordFailedAttempt();
        return SecurityValidationResult.invalid(
          'Invalid email format.',
        );
      }

      // Check password strength (for sign up)
      final passwordStrength = _securityService.checkPasswordStrength(password);
      if (passwordStrength.isWeak) {
        return SecurityValidationResult.weakPassword(
          'Password is too weak.',
          passwordStrength.missingRequirements,
        );
      }

      return SecurityValidationResult.valid();
    } catch (e) {
      if (kDebugMode) {
        print('Error validating auth attempt: $e');
      }
      return SecurityValidationResult.error('Validation failed.');
    }
  }

  /// Record failed authentication attempt
  void _recordFailedAttempt() {
    _failedAttempts++;
    
    if (_failedAttempts >= _maxFailedAttempts) {
      _lockAccount();
    }
    
    if (kDebugMode) {
      print('Failed attempts: $_failedAttempts/$_maxFailedAttempts');
    }
  }

  /// Lock account temporarily
  void _lockAccount() {
    _lockoutUntil = DateTime.now().add(_lockoutDuration);
    
    // Set timer to unlock account
    _lockoutTimer?.cancel();
    _lockoutTimer = Timer(_lockoutDuration, () {
      _unlockAccount();
    });
    
    if (kDebugMode) {
      print('Account locked until: $_lockoutUntil');
    }
  }

  /// Unlock account
  void _unlockAccount() {
    _failedAttempts = 0;
    _lockoutUntil = null;
    _lockoutTimer?.cancel();
    _lockoutTimer = null;
    
    if (kDebugMode) {
      print('Account unlocked');
    }
  }

  /// Check if account is locked out
  bool isLockedOut() {
    if (_lockoutUntil == null) return false;
    
    if (DateTime.now().isAfter(_lockoutUntil!)) {
      _unlockAccount();
      return false;
    }
    
    return true;
  }

  /// Get remaining lockout time
  Duration? getRemainingLockoutTime() {
    if (_lockoutUntil == null) return null;
    
    final now = DateTime.now();
    if (now.isAfter(_lockoutUntil!)) {
      return null;
    }
    
    return _lockoutUntil!.difference(now);
  }

  /// Reset failed attempts (on successful authentication)
  void resetFailedAttempts() {
    _failedAttempts = 0;
    _lockoutUntil = null;
    _lockoutTimer?.cancel();
    _lockoutTimer = null;
  }

  /// Validate biometric authentication
  Future<SecurityValidationResult> validateBiometricAuth() async {
    try {
      // Check if biometric is available
      final isAvailable = await _biometricService.isAvailable();
      if (!isAvailable) {
        return SecurityValidationResult.unavailable(
          'Biometric authentication is not available.',
        );
      }

      // Check if biometrics are enrolled
      final hasEnrolled = await _biometricService.hasEnrolledBiometrics();
      if (!hasEnrolled) {
        return SecurityValidationResult.notEnrolled(
          'No biometrics enrolled on this device.',
        );
      }

      return SecurityValidationResult.valid();
    } catch (e) {
      if (kDebugMode) {
        print('Error validating biometric auth: $e');
      }
      return SecurityValidationResult.error('Biometric validation failed.');
    }
  }

  /// Generate secure session token
  String generateSessionToken() {
    return _securityService.generateSessionToken();
  }

  /// Validate session token
  bool validateSessionToken(String token) {
    return _securityService.isValidJwtFormat(token) &&
           !_securityService.isJwtExpired(token);
  }

  /// Sanitize user input
  String sanitizeInput(String input) {
    return _securityService.sanitizeInput(input);
  }

  /// Get device security info
  Future<DeviceSecurityInfo> getDeviceSecurityInfo() async {
    return await _biometricService.getDeviceSecurityInfo();
  }

  /// Dispose resources
  void dispose() {
    _lockoutTimer?.cancel();
    
    if (kDebugMode) {
      print('Security middleware disposed');
    }
  }
}

/// Security validation result
class SecurityValidationResult {
  const SecurityValidationResult._({
    required this.isValid,
    required this.type,
    this.message,
    this.lockoutUntil,
    this.missingRequirements,
  });

  final bool isValid;
  final SecurityValidationType type;
  final String? message;
  final DateTime? lockoutUntil;
  final List<String>? missingRequirements;

  factory SecurityValidationResult.valid() {
    return const SecurityValidationResult._(
      isValid: true,
      type: SecurityValidationType.valid,
    );
  }

  factory SecurityValidationResult.invalid(String message) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.invalid,
      message: message,
    );
  }

  factory SecurityValidationResult.lockedOut(String message, DateTime until) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.lockedOut,
      message: message,
      lockoutUntil: until,
    );
  }

  factory SecurityValidationResult.suspicious(String message) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.suspicious,
      message: message,
    );
  }

  factory SecurityValidationResult.weakPassword(
    String message,
    List<String> requirements,
  ) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.weakPassword,
      message: message,
      missingRequirements: requirements,
    );
  }

  factory SecurityValidationResult.unavailable(String message) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.unavailable,
      message: message,
    );
  }

  factory SecurityValidationResult.notEnrolled(String message) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.notEnrolled,
      message: message,
    );
  }

  factory SecurityValidationResult.error(String message) {
    return SecurityValidationResult._(
      isValid: false,
      type: SecurityValidationType.error,
      message: message,
    );
  }
}

/// Security validation types
enum SecurityValidationType {
  valid,
  invalid,
  lockedOut,
  suspicious,
  weakPassword,
  unavailable,
  notEnrolled,
  error,
}

/// Extension for SecurityValidationType
extension SecurityValidationTypeExtension on SecurityValidationType {
  bool get isValid => this == SecurityValidationType.valid;
  bool get isInvalid => this == SecurityValidationType.invalid;
  bool get isLockedOut => this == SecurityValidationType.lockedOut;
  bool get isSuspicious => this == SecurityValidationType.suspicious;
  bool get isWeakPassword => this == SecurityValidationType.weakPassword;
  bool get isUnavailable => this == SecurityValidationType.unavailable;
  bool get isNotEnrolled => this == SecurityValidationType.notEnrolled;
  bool get isError => this == SecurityValidationType.error;
}
