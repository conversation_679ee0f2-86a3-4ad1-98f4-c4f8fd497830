import 'package:flutter/material.dart';

/// Color palette and theme definitions for the bidtrakr application
class AppColors {
  AppColors._();

  // Primary brand colors
  static const Color primaryBlue = Color(0xFF2563EB);
  static const Color primaryBlueLight = Color(0xFF3B82F6);
  static const Color primaryBlueDark = Color(0xFF1D4ED8);

  // Secondary colors
  static const Color secondaryGreen = Color(0xFF10B981);
  static const Color secondaryGreenLight = Color(0xFF34D399);
  static const Color secondaryGreenDark = Color(0xFF059669);

  // Accent colors
  static const Color accentOrange = Color(0xFFF59E0B);
  static const Color accentOrangeLight = Color(0xFFFBBF24);
  static const Color accentOrangeDark = Color(0xFFD97706);

  // Status colors
  static const Color successGreen = Color(0xFF10B981);
  static const Color warningYellow = Color(0xFFF59E0B);
  static const Color errorRed = Color(0xFFEF4444);
  static const Color infoBlue = Color(0xFF3B82F6);

  // Neutral colors - Light theme
  static const Color lightBackground = Color(0xFFFAFAFA);
  static const Color lightSurface = Color(0xFFFFFFFF);
  static const Color lightSurfaceVariant = Color(0xFFF3F4F6);
  static const Color lightOnBackground = Color(0xFF111827);
  static const Color lightOnSurface = Color(0xFF111827);
  static const Color lightOnSurfaceVariant = Color(0xFF6B7280);

  // Neutral colors - Dark theme
  static const Color darkBackground = Color(0xFF0F172A);
  static const Color darkSurface = Color(0xFF1E293B);
  static const Color darkSurfaceVariant = Color(0xFF334155);
  static const Color darkOnBackground = Color(0xFFF8FAFC);
  static const Color darkOnSurface = Color(0xFFF8FAFC);
  static const Color darkOnSurfaceVariant = Color(0xFFCBD5E1);

  // Border and outline colors
  static const Color lightOutline = Color(0xFFE5E7EB);
  static const Color darkOutline = Color(0xFF475569);

  // Shadow colors
  static const Color lightShadow = Color(0xFF000000);
  static const Color darkShadow = Color(0xFF000000);

  // Income tracking colors
  static const Color incomePositive = Color(0xFF10B981);
  static const Color incomeNegative = Color(0xFFEF4444);
  static const Color incomeNeutral = Color(0xFF6B7280);

  // Order status colors
  static const Color orderCompleted = Color(0xFF10B981);
  static const Color orderMissed = Color(0xFFEF4444);
  static const Color orderCanceled = Color(0xFFF59E0B);
  static const Color orderPending = Color(0xFF6B7280);

  // Performance level colors
  static const Color levelPlatinum = Color(0xFFE5E7EB);
  static const Color levelGold = Color(0xFFFBBF24);
  static const Color levelSilver = Color(0xFF9CA3AF);
  static const Color levelBronze = Color(0xFFD97706);

  // Spare parts status colors
  static const Color sparePartsGood = Color(0xFF10B981);
  static const Color sparePartsWarning = Color(0xFFF59E0B);
  static const Color sparePartsCritical = Color(0xFFEF4444);

  // Chart colors
  static const List<Color> chartColors = [
    Color(0xFF2563EB), // Blue
    Color(0xFF10B981), // Green
    Color(0xFFF59E0B), // Orange
    Color(0xFFEF4444), // Red
    Color(0xFF8B5CF6), // Purple
    Color(0xFF06B6D4), // Cyan
    Color(0xFFEC4899), // Pink
    Color(0xFF84CC16), // Lime
  ];

  // Payment method colors
  static const Color goPayGreen = Color(0xFF00AA5B);
  static const Color bcaBlue = Color(0xFF003D79);
  static const Color cashGray = Color(0xFF6B7280);
  static const Color ovoBlue = Color(0xFF4C3F91);
  static const Color briBlue = Color(0xFF003D79);
  static const Color rekponOrange = Color(0xFFFF6B35);

  /// Light theme color scheme
  static const ColorScheme lightColorScheme = ColorScheme(
    brightness: Brightness.light,
    primary: primaryBlue,
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFDBE7FF),
    onPrimaryContainer: Color(0xFF001C3B),
    secondary: secondaryGreen,
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFA7F3D0),
    onSecondaryContainer: Color(0xFF002114),
    tertiary: accentOrange,
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFFFE4B5),
    onTertiaryContainer: Color(0xFF2A1800),
    error: errorRed,
    onError: Color(0xFFFFFFFF),
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    background: lightBackground,
    onBackground: lightOnBackground,
    surface: lightSurface,
    onSurface: lightOnSurface,
    surfaceVariant: lightSurfaceVariant,
    onSurfaceVariant: lightOnSurfaceVariant,
    outline: lightOutline,
    outlineVariant: Color(0xFFE5E7EB),
    shadow: lightShadow,
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFF2F3349),
    onInverseSurface: Color(0xFFF1F0F4),
    inversePrimary: Color(0xFFB4CCFF),
    surfaceTint: primaryBlue,
  );

  /// Dark theme color scheme
  static const ColorScheme darkColorScheme = ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFB4CCFF),
    onPrimary: Color(0xFF002E69),
    primaryContainer: Color(0xFF004494),
    onPrimaryContainer: Color(0xFFDBE7FF),
    secondary: Color(0xFF8BF5BA),
    onSecondary: Color(0xFF003826),
    secondaryContainer: Color(0xFF005138),
    onSecondaryContainer: Color(0xFFA7F3D0),
    tertiary: Color(0xFFFFCC7A),
    onTertiary: Color(0xFF452B00),
    tertiaryContainer: Color(0xFF633F00),
    onTertiaryContainer: Color(0xFFFFE4B5),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    background: darkBackground,
    onBackground: darkOnBackground,
    surface: darkSurface,
    onSurface: darkOnSurface,
    surfaceVariant: darkSurfaceVariant,
    onSurfaceVariant: darkOnSurfaceVariant,
    outline: darkOutline,
    outlineVariant: Color(0xFF475569),
    shadow: darkShadow,
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: primaryBlue,
    surfaceTint: Color(0xFFB4CCFF),
  );

  /// Get color for income value (positive, negative, neutral)
  static Color getIncomeColor(double value) {
    if (value > 0) return incomePositive;
    if (value < 0) return incomeNegative;
    return incomeNeutral;
  }

  /// Get color for order status
  static Color getOrderStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return orderCompleted;
      case 'missed':
        return orderMissed;
      case 'canceled':
        return orderCanceled;
      default:
        return orderPending;
    }
  }

  /// Get color for performance level
  static Color getLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'platinum':
        return levelPlatinum;
      case 'gold':
        return levelGold;
      case 'silver':
        return levelSilver;
      case 'bronze':
        return levelBronze;
      default:
        return levelBronze;
    }
  }

  /// Get color for spare parts status based on usage percentage
  static Color getSparePartsStatusColor(double usagePercentage) {
    if (usagePercentage >= 80) return sparePartsCritical;
    if (usagePercentage >= 60) return sparePartsWarning;
    return sparePartsGood;
  }

  /// Get color for payment method
  static Color getPaymentMethodColor(String method) {
    switch (method.toLowerCase()) {
      case 'gopay':
        return goPayGreen;
      case 'bca':
        return bcaBlue;
      case 'cash':
        return cashGray;
      case 'ovo':
        return ovoBlue;
      case 'bri':
        return briBlue;
      case 'rekpon':
        return rekponOrange;
      default:
        return primaryBlue;
    }
  }

  /// Get chart color by index
  static Color getChartColor(int index) {
    return chartColors[index % chartColors.length];
  }

  /// Get gradient for income cards
  static LinearGradient get incomeGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF2563EB),
      Color(0xFF3B82F6),
    ],
  );

  /// Get gradient for order cards
  static LinearGradient get orderGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF10B981),
      Color(0xFF34D399),
    ],
  );

  /// Get gradient for performance cards
  static LinearGradient get performanceGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFFF59E0B),
      Color(0xFFFBBF24),
    ],
  );

  /// Get gradient for spare parts cards
  static LinearGradient get sparePartsGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [
      Color(0xFF8B5CF6),
      Color(0xFFA78BFA),
    ],
  );
}
