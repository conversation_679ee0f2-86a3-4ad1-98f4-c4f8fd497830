import 'package:dartz/dartz.dart';
import '../repositories/auth_repository.dart';
import '../errors/failures.dart';
import '../models/auth_models.dart';

/// Authentication service for handling business logic
class AuthService {
  AuthService({
    required AuthRepository authRepository,
  }) : _authRepository = authRepository;

  final AuthRepository _authRepository;

  /// Sign up a new user
  Future<Either<Failure, AuthUserModel>> signUp({
    required String email,
    required String password,
    String? name,
    String? phone,
    Map<String, dynamic>? metadata,
  }) async {
    final request = SignUpRequest(
      email: email.trim().toLowerCase(),
      password: password,
      name: name?.trim(),
      phone: phone?.trim(),
      metadata: metadata ?? {},
    );

    return await _authRepository.signUp(request);
  }

  /// Sign in with email and password
  Future<Either<Failure, AuthUserModel>> signInWithPassword({
    required String email,
    required String password,
  }) async {
    final request = SignInRequest(
      email: email.trim().toLowerCase(),
      password: password,
    );

    return await _authRepository.signInWithPassword(request);
  }

  /// Sign in with magic link (OTP)
  Future<Either<Failure, void>> signInWithMagicLink({
    required String email,
    String? redirectTo,
  }) async {
    return await _authRepository.signInWithOtp(
      email.trim().toLowerCase(),
      redirectTo: redirectTo,
    );
  }

  /// Verify OTP code
  Future<Either<Failure, AuthUserModel>> verifyOtp({
    required String email,
    required String token,
    required OtpType type,
  }) async {
    final request = OtpVerificationRequest(
      email: email.trim().toLowerCase(),
      token: token.trim(),
      type: type,
    );

    return await _authRepository.verifyOtp(request);
  }

  /// Sign out current user
  Future<Either<Failure, void>> signOut() async {
    return await _authRepository.signOut();
  }

  /// Refresh current session
  Future<Either<Failure, AuthUserModel>> refreshSession() async {
    return await _authRepository.refreshSession();
  }

  /// Reset password
  Future<Either<Failure, void>> resetPassword({
    required String email,
    String? redirectTo,
  }) async {
    final request = PasswordResetRequest(
      email: email.trim().toLowerCase(),
      redirectTo: redirectTo,
    );

    return await _authRepository.resetPassword(request);
  }

  /// Update user profile
  Future<Either<Failure, AuthUserModel>> updateProfile({
    String? email,
    String? password,
    String? name,
    String? phone,
    String? avatarUrl,
    Map<String, dynamic>? metadata,
  }) async {
    final request = UpdateUserRequest(
      email: email?.trim().toLowerCase(),
      password: password,
      name: name?.trim(),
      phone: phone?.trim(),
      avatarUrl: avatarUrl?.trim(),
      metadata: metadata ?? {},
    );

    return await _authRepository.updateUser(request);
  }

  /// Get current authenticated user
  Future<Either<Failure, AuthUserModel?>> getCurrentUser() async {
    return await _authRepository.getCurrentUser();
  }

  /// Get current session
  Future<Either<Failure, SessionModel?>> getCurrentSession() async {
    return await _authRepository.getCurrentSession();
  }

  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated() async {
    return await _authRepository.isAuthenticated();
  }

  /// Listen to authentication state changes
  Stream<AuthStateModel> get authStateChanges {
    return _authRepository.authStateChanges;
  }

  /// Enable or disable biometric authentication
  Future<Either<Failure, void>> setBiometricEnabled(bool enabled) async {
    return await _authRepository.setBiometricEnabled(enabled);
  }

  /// Check if biometric authentication is enabled
  Future<Either<Failure, bool>> isBiometricEnabled() async {
    return await _authRepository.isBiometricEnabled();
  }

  /// Store user preferences
  Future<Either<Failure, void>> storeUserPreferences(
    Map<String, dynamic> preferences,
  ) async {
    return await _authRepository.storeUserPreferences(preferences);
  }

  /// Get user preferences
  Future<Either<Failure, Map<String, dynamic>?>> getUserPreferences() async {
    return await _authRepository.getUserPreferences();
  }

  /// Clear all authentication data
  Future<Either<Failure, void>> clearAllData() async {
    return await _authRepository.clearAllData();
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Validate password strength
  bool isStrongPassword(String password) {
    if (password.length < 8) return false;
    
    final hasUppercase = RegExp(r'[A-Z]').hasMatch(password);
    final hasLowercase = RegExp(r'[a-z]').hasMatch(password);
    final hasDigits = RegExp(r'\d').hasMatch(password);
    final hasSpecialCharacters = RegExp(r'[@$!%*?&]').hasMatch(password);
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// Get password strength score (0-4)
  int getPasswordStrength(String password) {
    int score = 0;
    
    if (password.length >= 8) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'\d').hasMatch(password)) score++;
    if (RegExp(r'[@$!%*?&]').hasMatch(password)) score++;
    
    return score;
  }

  /// Get password strength description
  String getPasswordStrengthDescription(String password) {
    final strength = getPasswordStrength(password);
    
    switch (strength) {
      case 0:
      case 1:
        return 'Very Weak';
      case 2:
        return 'Weak';
      case 3:
        return 'Fair';
      case 4:
        return 'Good';
      case 5:
        return 'Strong';
      default:
        return 'Unknown';
    }
  }

  /// Validate phone number format
  bool isValidPhone(String phone) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phone);
  }

  /// Check if session is expired
  bool isSessionExpired(SessionModel session) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return session.expiresAt <= now;
  }

  /// Check if session needs refresh (expires in less than 5 minutes)
  bool shouldRefreshSession(SessionModel session) {
    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    const fiveMinutes = 5 * 60; // 5 minutes in seconds
    return (session.expiresAt - now) <= fiveMinutes;
  }

  /// Generate a secure random password
  String generateSecurePassword({int length = 12}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789@#\$%^&*';
    final random = DateTime.now().millisecondsSinceEpoch;
    
    String password = '';
    for (int i = 0; i < length; i++) {
      password += chars[(random + i) % chars.length];
    }
    
    return password;
  }

  /// Sanitize user input
  String sanitizeInput(String input) {
    return input.trim().replaceAll(RegExp(r'[<>"\']'), '');
  }

  /// Check if user has completed profile setup
  bool hasCompleteProfile(AuthUserModel user) {
    return user.name != null && 
           user.name!.isNotEmpty && 
           user.email.isNotEmpty;
  }

  /// Get user display name
  String getUserDisplayName(AuthUserModel user) {
    if (user.name != null && user.name!.isNotEmpty) {
      return user.name!;
    }
    
    // Extract name from email
    final emailParts = user.email.split('@');
    if (emailParts.isNotEmpty) {
      return emailParts.first;
    }
    
    return 'User';
  }

  /// Get user initials for avatar
  String getUserInitials(AuthUserModel user) {
    final displayName = getUserDisplayName(user);
    final words = displayName.split(' ');
    
    if (words.length >= 2) {
      return '${words.first[0]}${words[1][0]}'.toUpperCase();
    } else if (words.isNotEmpty && words.first.isNotEmpty) {
      return words.first[0].toUpperCase();
    }
    
    return 'U';
  }
}
