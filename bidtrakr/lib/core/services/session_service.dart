import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/auth_models.dart';
import '../services/auth_service.dart';
import '../errors/failures.dart';

/// Session management service
class SessionService {
  SessionService({
    required AuthService authService,
  }) : _authService = authService;

  final AuthService _authService;
  Timer? _refreshTimer;
  Timer? _expirationTimer;
  StreamSubscription<AuthStateModel>? _authStateSubscription;

  /// Initialize session management
  Future<void> initialize() async {
    // Listen to auth state changes
    _authStateSubscription = _authService.authStateChanges.listen(
      _handleAuthStateChange,
      onError: (error) {
        if (kDebugMode) {
          print('Auth state change error: $error');
        }
      },
    );

    // Check current session and set up timers
    await _checkAndSetupSession();
  }

  /// Dispose session management
  void dispose() {
    _refreshTimer?.cancel();
    _expirationTimer?.cancel();
    _authStateSubscription?.cancel();
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(AuthStateModel authState) {
    if (authState.isSignedIn && authState.session != null) {
      _setupSessionTimers(authState.session!);
    } else if (authState.isSignedOut) {
      _clearSessionTimers();
    }
  }

  /// Check current session and set up timers if needed
  Future<void> _checkAndSetupSession() async {
    final sessionResult = await _authService.getCurrentSession();
    
    sessionResult.fold(
      (failure) {
        if (kDebugMode) {
          print('Failed to get current session: ${failure.message}');
        }
      },
      (session) {
        if (session != null) {
          _setupSessionTimers(session);
        }
      },
    );
  }

  /// Set up session refresh and expiration timers
  void _setupSessionTimers(SessionModel session) {
    _clearSessionTimers();

    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    final expiresAt = session.expiresAt;
    
    // Calculate time until expiration
    final timeUntilExpiration = expiresAt - now;
    
    if (timeUntilExpiration <= 0) {
      // Session is already expired
      _handleSessionExpired();
      return;
    }

    // Set up refresh timer (refresh 5 minutes before expiration)
    const refreshBuffer = 5 * 60; // 5 minutes in seconds
    final timeUntilRefresh = timeUntilExpiration - refreshBuffer;
    
    if (timeUntilRefresh > 0) {
      _refreshTimer = Timer(
        Duration(seconds: timeUntilRefresh),
        _refreshSession,
      );
      
      if (kDebugMode) {
        print('Session refresh timer set for ${timeUntilRefresh}s');
      }
    } else {
      // Need to refresh immediately
      _refreshSession();
    }

    // Set up expiration timer
    _expirationTimer = Timer(
      Duration(seconds: timeUntilExpiration),
      _handleSessionExpired,
    );
    
    if (kDebugMode) {
      print('Session expiration timer set for ${timeUntilExpiration}s');
    }
  }

  /// Clear session timers
  void _clearSessionTimers() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
    
    _expirationTimer?.cancel();
    _expirationTimer = null;
  }

  /// Refresh the current session
  Future<void> _refreshSession() async {
    if (kDebugMode) {
      print('Refreshing session...');
    }

    final result = await _authService.refreshSession();
    
    result.fold(
      (failure) {
        if (kDebugMode) {
          print('Session refresh failed: ${failure.message}');
        }
        
        // If refresh fails, handle as expired session
        _handleSessionExpired();
      },
      (user) {
        if (kDebugMode) {
          print('Session refreshed successfully');
        }
        
        // Set up new timers with the refreshed session
        if (user.session != null) {
          _setupSessionTimers(user.session!);
        }
      },
    );
  }

  /// Handle session expiration
  void _handleSessionExpired() {
    if (kDebugMode) {
      print('Session expired, signing out...');
    }

    _clearSessionTimers();
    
    // Sign out the user
    _authService.signOut().then((result) {
      result.fold(
        (failure) {
          if (kDebugMode) {
            print('Failed to sign out expired user: ${failure.message}');
          }
        },
        (_) {
          if (kDebugMode) {
            print('User signed out due to session expiration');
          }
        },
      );
    });
  }

  /// Check if current session is valid
  Future<bool> isSessionValid() async {
    final sessionResult = await _authService.getCurrentSession();
    
    return sessionResult.fold(
      (failure) => false,
      (session) {
        if (session == null) return false;
        
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        return session.expiresAt > now;
      },
    );
  }

  /// Get time until session expires (in seconds)
  Future<int?> getTimeUntilExpiration() async {
    final sessionResult = await _authService.getCurrentSession();
    
    return sessionResult.fold(
      (failure) => null,
      (session) {
        if (session == null) return null;
        
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        final timeUntilExpiration = session.expiresAt - now;
        
        return timeUntilExpiration > 0 ? timeUntilExpiration : 0;
      },
    );
  }

  /// Force refresh session
  Future<bool> forceRefreshSession() async {
    final result = await _authService.refreshSession();
    
    return result.fold(
      (failure) {
        if (kDebugMode) {
          print('Force refresh failed: ${failure.message}');
        }
        return false;
      },
      (user) {
        if (kDebugMode) {
          print('Force refresh successful');
        }
        
        if (user.session != null) {
          _setupSessionTimers(user.session!);
        }
        
        return true;
      },
    );
  }

  /// Get session info for debugging
  Future<Map<String, dynamic>?> getSessionInfo() async {
    if (!kDebugMode) return null;

    final sessionResult = await _authService.getCurrentSession();
    
    return sessionResult.fold(
      (failure) => {
        'error': failure.message,
        'hasSession': false,
      },
      (session) {
        if (session == null) {
          return {
            'hasSession': false,
          };
        }

        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        final timeUntilExpiration = session.expiresAt - now;
        
        return {
          'hasSession': true,
          'expiresAt': DateTime.fromMillisecondsSinceEpoch(session.expiresAt * 1000).toIso8601String(),
          'timeUntilExpiration': timeUntilExpiration,
          'isExpired': timeUntilExpiration <= 0,
          'needsRefresh': timeUntilExpiration <= 300, // 5 minutes
          'tokenType': session.tokenType,
          'hasRefreshTimer': _refreshTimer != null,
          'hasExpirationTimer': _expirationTimer != null,
        };
      },
    );
  }

  /// Manually trigger session cleanup
  Future<void> cleanupSession() async {
    _clearSessionTimers();
    
    final result = await _authService.signOut();
    result.fold(
      (failure) {
        if (kDebugMode) {
          print('Session cleanup failed: ${failure.message}');
        }
      },
      (_) {
        if (kDebugMode) {
          print('Session cleaned up successfully');
        }
      },
    );
  }

  /// Check if session needs immediate refresh
  Future<bool> needsImmediateRefresh() async {
    final sessionResult = await _authService.getCurrentSession();
    
    return sessionResult.fold(
      (failure) => false,
      (session) {
        if (session == null) return false;
        
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        const refreshBuffer = 5 * 60; // 5 minutes
        
        return (session.expiresAt - now) <= refreshBuffer;
      },
    );
  }

  /// Get session status
  Future<SessionStatus> getSessionStatus() async {
    final sessionResult = await _authService.getCurrentSession();
    
    return sessionResult.fold(
      (failure) => SessionStatus.invalid,
      (session) {
        if (session == null) return SessionStatus.none;
        
        final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
        final timeUntilExpiration = session.expiresAt - now;
        
        if (timeUntilExpiration <= 0) {
          return SessionStatus.expired;
        } else if (timeUntilExpiration <= 300) { // 5 minutes
          return SessionStatus.needsRefresh;
        } else {
          return SessionStatus.valid;
        }
      },
    );
  }
}

/// Session status enumeration
enum SessionStatus {
  /// No session exists
  none,
  /// Session is valid and not expiring soon
  valid,
  /// Session exists but needs refresh soon
  needsRefresh,
  /// Session has expired
  expired,
  /// Session is invalid or corrupted
  invalid,
}

/// Extension for SessionStatus
extension SessionStatusExtension on SessionStatus {
  bool get isValid => this == SessionStatus.valid;
  bool get needsRefresh => this == SessionStatus.needsRefresh;
  bool get isExpired => this == SessionStatus.expired;
  bool get isInvalid => this == SessionStatus.invalid;
  bool get hasNoSession => this == SessionStatus.none;
  
  String get description {
    switch (this) {
      case SessionStatus.none:
        return 'No session';
      case SessionStatus.valid:
        return 'Valid session';
      case SessionStatus.needsRefresh:
        return 'Session needs refresh';
      case SessionStatus.expired:
        return 'Session expired';
      case SessionStatus.invalid:
        return 'Invalid session';
    }
  }
}
