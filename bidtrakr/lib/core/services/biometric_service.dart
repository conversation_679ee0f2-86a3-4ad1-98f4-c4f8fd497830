import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../errors/exceptions.dart';

/// Biometric authentication service
class BiometricService {
  BiometricService({
    LocalAuthentication? localAuth,
    DeviceInfoPlugin? deviceInfo,
  })  : _localAuth = localAuth ?? LocalAuthentication(),
        _deviceInfo = deviceInfo ?? DeviceInfoPlugin();

  final LocalAuthentication _localAuth;
  final DeviceInfoPlugin _deviceInfo;

  /// Check if biometric authentication is available on the device
  Future<bool> isAvailable() async {
    try {
      final isAvailable = await _localAuth.isDeviceSupported();
      final canCheckBiometrics = await _localAuth.canCheckBiometrics;
      
      return isAvailable && canCheckBiometrics;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking biometric availability: $e');
      }
      return false;
    }
  }

  /// Get available biometric types
  Future<List<BiometricType>> getAvailableBiometrics() async {
    try {
      return await _localAuth.getAvailableBiometrics();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting available biometrics: $e');
      }
      return [];
    }
  }

  /// Check if device has enrolled biometrics
  Future<bool> hasEnrolledBiometrics() async {
    try {
      final availableBiometrics = await getAvailableBiometrics();
      return availableBiometrics.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking enrolled biometrics: $e');
      }
      return false;
    }
  }

  /// Authenticate using biometrics
  Future<BiometricAuthResult> authenticate({
    String localizedReason = 'Please authenticate to access your account',
    bool biometricOnly = false,
    bool stickyAuth = true,
    bool sensitiveTransaction = true,
  }) async {
    try {
      // Check if biometrics are available
      if (!await isAvailable()) {
        return BiometricAuthResult.unavailable;
      }

      // Check if biometrics are enrolled
      if (!await hasEnrolledBiometrics()) {
        return BiometricAuthResult.notEnrolled;
      }

      // Perform authentication
      final isAuthenticated = await _localAuth.authenticate(
        localizedReason: localizedReason,
        options: AuthenticationOptions(
          biometricOnly: biometricOnly,
          stickyAuth: stickyAuth,
          sensitiveTransaction: sensitiveTransaction,
        ),
      );

      return isAuthenticated 
          ? BiometricAuthResult.success 
          : BiometricAuthResult.failed;
          
    } on PlatformException catch (e) {
      return _handlePlatformException(e);
    } catch (e) {
      if (kDebugMode) {
        print('Biometric authentication error: $e');
      }
      return BiometricAuthResult.error;
    }
  }

  /// Handle platform exceptions from biometric authentication
  BiometricAuthResult _handlePlatformException(PlatformException e) {
    switch (e.code) {
      case 'NotAvailable':
        return BiometricAuthResult.unavailable;
      case 'NotEnrolled':
        return BiometricAuthResult.notEnrolled;
      case 'LockedOut':
        return BiometricAuthResult.lockedOut;
      case 'PermanentlyLockedOut':
        return BiometricAuthResult.permanentlyLockedOut;
      case 'UserCancel':
        return BiometricAuthResult.userCancel;
      case 'UserFallback':
        return BiometricAuthResult.userFallback;
      case 'BiometricOnlyNotSupported':
        return BiometricAuthResult.biometricOnlyNotSupported;
      case 'DeviceNotSupported':
        return BiometricAuthResult.deviceNotSupported;
      case 'InvalidContext':
        return BiometricAuthResult.invalidContext;
      case 'NotImplemented':
        return BiometricAuthResult.notImplemented;
      default:
        if (kDebugMode) {
          print('Unknown biometric error: ${e.code} - ${e.message}');
        }
        return BiometricAuthResult.error;
    }
  }

  /// Get biometric capability information
  Future<BiometricCapability> getBiometricCapability() async {
    try {
      final isSupported = await _localAuth.isDeviceSupported();
      final canCheck = await _localAuth.canCheckBiometrics;
      final availableBiometrics = await getAvailableBiometrics();
      
      return BiometricCapability(
        isSupported: isSupported,
        canCheckBiometrics: canCheck,
        availableBiometrics: availableBiometrics,
        hasEnrolledBiometrics: availableBiometrics.isNotEmpty,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting biometric capability: $e');
      }
      return BiometricCapability(
        isSupported: false,
        canCheckBiometrics: false,
        availableBiometrics: [],
        hasEnrolledBiometrics: false,
      );
    }
  }

  /// Get device security information
  Future<DeviceSecurityInfo> getDeviceSecurityInfo() async {
    try {
      final deviceInfo = await _deviceInfo.deviceInfo;
      final biometricCapability = await getBiometricCapability();
      
      if (deviceInfo is AndroidDeviceInfo) {
        return DeviceSecurityInfo(
          platform: 'Android',
          model: deviceInfo.model,
          version: deviceInfo.version.release,
          securityPatch: deviceInfo.version.securityPatch,
          biometricCapability: biometricCapability,
          hasSecureHardware: deviceInfo.isPhysicalDevice,
        );
      } else if (deviceInfo is IosDeviceInfo) {
        return DeviceSecurityInfo(
          platform: 'iOS',
          model: deviceInfo.model,
          version: deviceInfo.systemVersion,
          biometricCapability: biometricCapability,
          hasSecureHardware: true, // iOS devices have secure enclave
        );
      } else {
        return DeviceSecurityInfo(
          platform: 'Unknown',
          biometricCapability: biometricCapability,
          hasSecureHardware: false,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting device security info: $e');
      }
      return DeviceSecurityInfo(
        platform: 'Unknown',
        biometricCapability: BiometricCapability(
          isSupported: false,
          canCheckBiometrics: false,
          availableBiometrics: [],
          hasEnrolledBiometrics: false,
        ),
        hasSecureHardware: false,
      );
    }
  }

  /// Stop authentication (if in progress)
  Future<bool> stopAuthentication() async {
    try {
      return await _localAuth.stopAuthentication();
    } catch (e) {
      if (kDebugMode) {
        print('Error stopping authentication: $e');
      }
      return false;
    }
  }

  /// Get user-friendly biometric type names
  String getBiometricTypeName(BiometricType type) {
    switch (type) {
      case BiometricType.face:
        return 'Face ID';
      case BiometricType.fingerprint:
        return 'Fingerprint';
      case BiometricType.iris:
        return 'Iris';
      case BiometricType.weak:
        return 'Weak Biometric';
      case BiometricType.strong:
        return 'Strong Biometric';
    }
  }

  /// Get available biometric names as a formatted string
  Future<String> getAvailableBiometricNames() async {
    final biometrics = await getAvailableBiometrics();
    if (biometrics.isEmpty) return 'None';
    
    return biometrics
        .map((type) => getBiometricTypeName(type))
        .join(', ');
  }
}

/// Biometric authentication result
enum BiometricAuthResult {
  success,
  failed,
  unavailable,
  notEnrolled,
  lockedOut,
  permanentlyLockedOut,
  userCancel,
  userFallback,
  biometricOnlyNotSupported,
  deviceNotSupported,
  invalidContext,
  notImplemented,
  error,
}

/// Extension for BiometricAuthResult
extension BiometricAuthResultExtension on BiometricAuthResult {
  bool get isSuccess => this == BiometricAuthResult.success;
  bool get isFailed => this == BiometricAuthResult.failed;
  bool get isUnavailable => this == BiometricAuthResult.unavailable;
  bool get isNotEnrolled => this == BiometricAuthResult.notEnrolled;
  bool get isLockedOut => this == BiometricAuthResult.lockedOut;
  bool get isPermanentlyLockedOut => this == BiometricAuthResult.permanentlyLockedOut;
  bool get isUserCancel => this == BiometricAuthResult.userCancel;
  bool get isError => this == BiometricAuthResult.error;

  String get description {
    switch (this) {
      case BiometricAuthResult.success:
        return 'Authentication successful';
      case BiometricAuthResult.failed:
        return 'Authentication failed';
      case BiometricAuthResult.unavailable:
        return 'Biometric authentication is not available';
      case BiometricAuthResult.notEnrolled:
        return 'No biometrics enrolled on this device';
      case BiometricAuthResult.lockedOut:
        return 'Biometric authentication is temporarily locked';
      case BiometricAuthResult.permanentlyLockedOut:
        return 'Biometric authentication is permanently locked';
      case BiometricAuthResult.userCancel:
        return 'Authentication cancelled by user';
      case BiometricAuthResult.userFallback:
        return 'User chose to use fallback authentication';
      case BiometricAuthResult.biometricOnlyNotSupported:
        return 'Biometric-only authentication not supported';
      case BiometricAuthResult.deviceNotSupported:
        return 'Device does not support biometric authentication';
      case BiometricAuthResult.invalidContext:
        return 'Invalid authentication context';
      case BiometricAuthResult.notImplemented:
        return 'Biometric authentication not implemented';
      case BiometricAuthResult.error:
        return 'An error occurred during authentication';
    }
  }
}

/// Biometric capability information
class BiometricCapability {
  const BiometricCapability({
    required this.isSupported,
    required this.canCheckBiometrics,
    required this.availableBiometrics,
    required this.hasEnrolledBiometrics,
  });

  final bool isSupported;
  final bool canCheckBiometrics;
  final List<BiometricType> availableBiometrics;
  final bool hasEnrolledBiometrics;

  bool get isFullyAvailable => isSupported && canCheckBiometrics && hasEnrolledBiometrics;
  bool get hasFaceID => availableBiometrics.contains(BiometricType.face);
  bool get hasFingerprint => availableBiometrics.contains(BiometricType.fingerprint);
  bool get hasIris => availableBiometrics.contains(BiometricType.iris);
}

/// Device security information
class DeviceSecurityInfo {
  const DeviceSecurityInfo({
    required this.platform,
    this.model,
    this.version,
    this.securityPatch,
    required this.biometricCapability,
    required this.hasSecureHardware,
  });

  final String platform;
  final String? model;
  final String? version;
  final String? securityPatch;
  final BiometricCapability biometricCapability;
  final bool hasSecureHardware;

  bool get isSecure => hasSecureHardware && biometricCapability.isFullyAvailable;
}
