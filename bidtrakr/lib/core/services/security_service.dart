import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../errors/exceptions.dart';

/// Security service for cryptographic operations and security checks
class SecurityService {
  SecurityService({
    DeviceInfoPlugin? deviceInfo,
  }) : _deviceInfo = deviceInfo ?? DeviceInfoPlugin();

  final DeviceInfoPlugin _deviceInfo;
  static const int _saltLength = 32;
  static const int _keyLength = 32;
  static const int _ivLength = 16;

  /// Generate a cryptographically secure random string
  String generateSecureRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    
    return List.generate(length, (index) => chars[random.nextInt(chars.length)])
        .join();
  }

  /// Generate a secure salt for password hashing
  List<int> generateSalt() {
    final random = Random.secure();
    return List.generate(_saltLength, (index) => random.nextInt(256));
  }

  /// Hash a password with salt using PBKDF2
  String hashPassword(String password, List<int> salt) {
    final passwordBytes = utf8.encode(password);
    final hmac = Hmac(sha256, salt);
    
    // Perform PBKDF2 with 10000 iterations
    var result = passwordBytes;
    for (int i = 0; i < 10000; i++) {
      result = hmac.convert(result).bytes;
    }
    
    return base64Encode(result);
  }

  /// Verify a password against its hash
  bool verifyPassword(String password, String hash, List<int> salt) {
    final computedHash = hashPassword(password, salt);
    return computedHash == hash;
  }

  /// Generate a secure API key
  String generateApiKey() {
    return generateSecureRandomString(64);
  }

  /// Generate a secure session token
  String generateSessionToken() {
    return generateSecureRandomString(128);
  }

  /// Generate a secure nonce
  String generateNonce() {
    return generateSecureRandomString(32);
  }

  /// Create a secure hash of data
  String createSecureHash(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Create HMAC signature
  String createHmacSignature(String data, String secret) {
    final key = utf8.encode(secret);
    final bytes = utf8.encode(data);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    return base64Encode(digest.bytes);
  }

  /// Verify HMAC signature
  bool verifyHmacSignature(String data, String signature, String secret) {
    final expectedSignature = createHmacSignature(data, secret);
    return expectedSignature == signature;
  }

  /// Encrypt data using AES (simplified implementation)
  Map<String, String> encryptData(String data, String key) {
    try {
      // This is a simplified implementation
      // In production, use proper AES encryption with a crypto library
      final dataBytes = utf8.encode(data);
      final keyBytes = utf8.encode(key.padRight(_keyLength).substring(0, _keyLength));
      
      // Generate IV
      final random = Random.secure();
      final iv = List.generate(_ivLength, (index) => random.nextInt(256));
      
      // Simple XOR encryption (NOT secure for production)
      final encrypted = <int>[];
      for (int i = 0; i < dataBytes.length; i++) {
        encrypted.add(dataBytes[i] ^ keyBytes[i % keyBytes.length] ^ iv[i % iv.length]);
      }
      
      return {
        'encrypted': base64Encode(encrypted),
        'iv': base64Encode(iv),
      };
    } catch (e) {
      throw SecurityException('Encryption failed: $e');
    }
  }

  /// Decrypt data using AES (simplified implementation)
  String decryptData(String encryptedData, String iv, String key) {
    try {
      // This is a simplified implementation
      // In production, use proper AES decryption with a crypto library
      final encryptedBytes = base64Decode(encryptedData);
      final ivBytes = base64Decode(iv);
      final keyBytes = utf8.encode(key.padRight(_keyLength).substring(0, _keyLength));
      
      // Simple XOR decryption (NOT secure for production)
      final decrypted = <int>[];
      for (int i = 0; i < encryptedBytes.length; i++) {
        decrypted.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length] ^ ivBytes[i % ivBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      throw SecurityException('Decryption failed: $e');
    }
  }

  /// Sanitize input to prevent injection attacks
  String sanitizeInput(String input) {
    return input
        .replaceAll(RegExp(r'[<>"\']'), '')
        .replaceAll(RegExp(r'[&]'), '&amp;')
        .trim();
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }

  /// Check password strength
  PasswordStrength checkPasswordStrength(String password) {
    int score = 0;
    final checks = <String>[];

    // Length check
    if (password.length >= 8) {
      score++;
    } else {
      checks.add('At least 8 characters');
    }

    // Uppercase check
    if (RegExp(r'[A-Z]').hasMatch(password)) {
      score++;
    } else {
      checks.add('At least one uppercase letter');
    }

    // Lowercase check
    if (RegExp(r'[a-z]').hasMatch(password)) {
      score++;
    } else {
      checks.add('At least one lowercase letter');
    }

    // Number check
    if (RegExp(r'\d').hasMatch(password)) {
      score++;
    } else {
      checks.add('At least one number');
    }

    // Special character check
    if (RegExp(r'[@$!%*?&]').hasMatch(password)) {
      score++;
    } else {
      checks.add('At least one special character (@\$!%*?&)');
    }

    // Common password check
    if (_isCommonPassword(password)) {
      score = 0;
      checks.add('Password is too common');
    }

    return PasswordStrength(
      score: score,
      maxScore: 5,
      missingRequirements: checks,
    );
  }

  /// Check if password is commonly used
  bool _isCommonPassword(String password) {
    final commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
    ];
    
    return commonPasswords.contains(password.toLowerCase());
  }

  /// Get device fingerprint for security
  Future<String> getDeviceFingerprint() async {
    try {
      final deviceInfo = await _deviceInfo.deviceInfo;
      final Map<String, dynamic> fingerprint = {};

      if (deviceInfo is AndroidDeviceInfo) {
        fingerprint.addAll({
          'platform': 'Android',
          'model': deviceInfo.model,
          'manufacturer': deviceInfo.manufacturer,
          'brand': deviceInfo.brand,
          'device': deviceInfo.device,
          'androidId': deviceInfo.id,
          'version': deviceInfo.version.release,
          'sdkInt': deviceInfo.version.sdkInt,
        });
      } else if (deviceInfo is IosDeviceInfo) {
        fingerprint.addAll({
          'platform': 'iOS',
          'model': deviceInfo.model,
          'name': deviceInfo.name,
          'systemName': deviceInfo.systemName,
          'systemVersion': deviceInfo.systemVersion,
          'identifierForVendor': deviceInfo.identifierForVendor,
        });
      }

      final fingerprintString = json.encode(fingerprint);
      return createSecureHash(fingerprintString);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting device fingerprint: $e');
      }
      return generateSecureRandomString(32);
    }
  }

  /// Validate JWT token format (basic validation)
  bool isValidJwtFormat(String token) {
    final parts = token.split('.');
    if (parts.length != 3) return false;

    try {
      // Try to decode each part
      for (final part in parts) {
        base64Decode(base64.normalize(part));
      }
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Extract JWT payload (without verification)
  Map<String, dynamic>? extractJwtPayload(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64.normalize(payload);
      final decoded = utf8.decode(base64Decode(normalized));
      
      return json.decode(decoded) as Map<String, dynamic>;
    } catch (e) {
      if (kDebugMode) {
        print('Error extracting JWT payload: $e');
      }
      return null;
    }
  }

  /// Check if JWT token is expired
  bool isJwtExpired(String token) {
    final payload = extractJwtPayload(token);
    if (payload == null) return true;

    final exp = payload['exp'] as int?;
    if (exp == null) return true;

    final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    return exp <= now;
  }

  /// Generate a secure device ID
  Future<String> generateDeviceId() async {
    final fingerprint = await getDeviceFingerprint();
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final random = generateSecureRandomString(16);
    
    final combined = '$fingerprint-$timestamp-$random';
    return createSecureHash(combined);
  }

  /// Validate input against common injection patterns
  bool containsSuspiciousPatterns(String input) {
    final suspiciousPatterns = [
      RegExp(r'<script', caseSensitive: false),
      RegExp(r'javascript:', caseSensitive: false),
      RegExp(r'on\w+\s*=', caseSensitive: false),
      RegExp(r'union\s+select', caseSensitive: false),
      RegExp(r'drop\s+table', caseSensitive: false),
      RegExp(r'insert\s+into', caseSensitive: false),
      RegExp(r'delete\s+from', caseSensitive: false),
      RegExp(r'update\s+set', caseSensitive: false),
    ];

    return suspiciousPatterns.any((pattern) => pattern.hasMatch(input));
  }

  /// Rate limiting check (simple implementation)
  bool checkRateLimit(String identifier, int maxAttempts, Duration window) {
    // This is a simplified implementation
    // In production, use a proper rate limiting solution with persistent storage
    final now = DateTime.now();
    final key = '$identifier-${now.millisecondsSinceEpoch ~/ window.inMilliseconds}';
    
    // This would need to be stored persistently
    // For now, just return true (allow)
    return true;
  }

  /// Generate a secure backup code
  String generateBackupCode() {
    final random = Random.secure();
    final code = List.generate(8, (index) => random.nextInt(10)).join();
    return '${code.substring(0, 4)}-${code.substring(4, 8)}';
  }

  /// Generate multiple backup codes
  List<String> generateBackupCodes(int count) {
    return List.generate(count, (index) => generateBackupCode());
  }
}

/// Password strength information
class PasswordStrength {
  const PasswordStrength({
    required this.score,
    required this.maxScore,
    required this.missingRequirements,
  });

  final int score;
  final int maxScore;
  final List<String> missingRequirements;

  double get percentage => score / maxScore;
  
  bool get isWeak => score <= 2;
  bool get isFair => score == 3;
  bool get isGood => score == 4;
  bool get isStrong => score == 5;

  String get description {
    if (isStrong) return 'Strong';
    if (isGood) return 'Good';
    if (isFair) return 'Fair';
    return 'Weak';
  }

  String get color {
    if (isStrong) return 'green';
    if (isGood) return 'lightgreen';
    if (isFair) return 'orange';
    return 'red';
  }
}
