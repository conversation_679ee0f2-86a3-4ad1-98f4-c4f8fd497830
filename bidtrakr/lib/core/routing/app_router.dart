import 'package:flutter/material.dart';
import '../../features/auth/screens/sign_in_screen.dart';
import '../../features/auth/screens/sign_up_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/dashboard/screens/dashboard_screen.dart';
import '../../features/trips/screens/trip_list_screen.dart';
import '../../features/trips/screens/add_trip_screen.dart';
import '../../features/trips/screens/trip_detail_screen.dart';
import '../../features/income/screens/income_overview_screen.dart';
import '../../features/expenses/screens/expense_list_screen.dart';
import '../../features/expenses/screens/add_expense_screen.dart';
import '../../features/maintenance/screens/maintenance_list_screen.dart';
import '../../features/maintenance/screens/add_maintenance_screen.dart';
import '../../features/analytics/screens/analytics_screen.dart';
import '../../features/settings/screens/settings_screen.dart';
import '../../features/profile/screens/profile_screen.dart';

/// Application router for handling navigation
class AppRouter {
  AppRouter._();

  /// Generate route based on route settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      // Authentication routes
      case SignInScreen.routeName:
        return _buildRoute(const SignInScreen(), settings);
      
      case SignUpScreen.routeName:
        return _buildRoute(const SignUpScreen(), settings);
      
      case ForgotPasswordScreen.routeName:
        return _buildRoute(const ForgotPasswordScreen(), settings);
      
      // Main app routes
      case DashboardScreen.routeName:
        return _buildRoute(const DashboardScreen(), settings);
      
      // Trip routes
      case TripListScreen.routeName:
        return _buildRoute(const TripListScreen(), settings);
      
      case AddTripScreen.routeName:
        return _buildRoute(const AddTripScreen(), settings);
      
      case TripDetailScreen.routeName:
        final tripId = settings.arguments as String?;
        if (tripId == null) {
          return _buildErrorRoute('Trip ID is required', settings);
        }
        return _buildRoute(TripDetailScreen(tripId: tripId), settings);
      
      // Income routes
      case IncomeOverviewScreen.routeName:
        return _buildRoute(const IncomeOverviewScreen(), settings);
      
      // Expense routes
      case ExpenseListScreen.routeName:
        return _buildRoute(const ExpenseListScreen(), settings);
      
      case AddExpenseScreen.routeName:
        return _buildRoute(const AddExpenseScreen(), settings);
      
      // Maintenance routes
      case MaintenanceListScreen.routeName:
        return _buildRoute(const MaintenanceListScreen(), settings);
      
      case AddMaintenanceScreen.routeName:
        return _buildRoute(const AddMaintenanceScreen(), settings);
      
      // Analytics routes
      case AnalyticsScreen.routeName:
        return _buildRoute(const AnalyticsScreen(), settings);
      
      // Settings routes
      case SettingsScreen.routeName:
        return _buildRoute(const SettingsScreen(), settings);
      
      // Profile routes
      case ProfileScreen.routeName:
        return _buildRoute(const ProfileScreen(), settings);
      
      // Default route
      default:
        return _buildErrorRoute('Route not found: ${settings.name}', settings);
    }
  }

  /// Build a standard page route
  static PageRoute<T> _buildRoute<T>(Widget page, RouteSettings settings) {
    return MaterialPageRoute<T>(
      builder: (_) => page,
      settings: settings,
    );
  }

  /// Build an error route for unknown routes
  static Route<dynamic> _buildErrorRoute(String message, RouteSettings settings) {
    return MaterialPageRoute(
      builder: (_) => RouteNotFoundScreen(
        routeName: settings.name ?? 'Unknown',
        message: message,
      ),
      settings: settings,
    );
  }

  /// Get initial route based on authentication state
  static String getInitialRoute(bool isAuthenticated) {
    return isAuthenticated ? DashboardScreen.routeName : SignInScreen.routeName;
  }

  /// Check if route requires authentication
  static bool requiresAuth(String? routeName) {
    const publicRoutes = {
      SignInScreen.routeName,
      SignUpScreen.routeName,
      ForgotPasswordScreen.routeName,
    };
    
    return routeName != null && !publicRoutes.contains(routeName);
  }

  /// Get route title for app bar
  static String getRouteTitle(String? routeName) {
    switch (routeName) {
      case SignInScreen.routeName:
        return 'Sign In';
      case SignUpScreen.routeName:
        return 'Sign Up';
      case ForgotPasswordScreen.routeName:
        return 'Reset Password';
      case DashboardScreen.routeName:
        return 'Dashboard';
      case TripListScreen.routeName:
        return 'Trips';
      case AddTripScreen.routeName:
        return 'Add Trip';
      case TripDetailScreen.routeName:
        return 'Trip Details';
      case IncomeOverviewScreen.routeName:
        return 'Income';
      case ExpenseListScreen.routeName:
        return 'Expenses';
      case AddExpenseScreen.routeName:
        return 'Add Expense';
      case MaintenanceListScreen.routeName:
        return 'Maintenance';
      case AddMaintenanceScreen.routeName:
        return 'Add Maintenance';
      case AnalyticsScreen.routeName:
        return 'Analytics';
      case SettingsScreen.routeName:
        return 'Settings';
      case ProfileScreen.routeName:
        return 'Profile';
      default:
        return 'BidTrakr';
    }
  }
}

/// Screen shown when a route is not found
class RouteNotFoundScreen extends StatelessWidget {
  const RouteNotFoundScreen({
    super.key,
    required this.routeName,
    required this.message,
  });

  final String routeName;
  final String message;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 80,
                color: theme.colorScheme.error,
              ),
              
              const SizedBox(height: 24),
              
              Text(
                'Page Not Found',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              Text(
                message,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 8),
              
              Text(
                'Route: $routeName',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  fontFamily: 'monospace',
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 32),
              
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).pushNamedAndRemoveUntil(
                    DashboardScreen.routeName,
                    (route) => false,
                  );
                },
                icon: const Icon(Icons.home),
                label: const Text('Go to Dashboard'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
