import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/app_providers.dart';
import '../../features/auth/screens/sign_in_screen.dart';
import 'app_router.dart';

/// Authentication guard widget that protects routes
class AuthGuard extends ConsumerWidget {
  const AuthGuard({
    super.key,
    required this.child,
    this.redirectTo,
  });

  final Widget child;
  final String? redirectTo;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);

    return authState.when(
      data: (user) {
        if (user != null) {
          // User is authenticated, show the protected content
          return child;
        } else {
          // User is not authenticated, redirect to sign in
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(context).pushNamedAndRemoveUntil(
              redirectTo ?? SignInScreen.routeName,
              (route) => false,
            );
          });
          return const AuthGuardLoadingScreen();
        }
      },
      loading: () => const AuthGuardLoadingScreen(),
      error: (error, stackTrace) => AuthGuardErrorScreen(
        error: error.toString(),
        onRetry: () {
          ref.read(authStateProvider.notifier).refresh();
        },
      ),
    );
  }
}

/// Loading screen shown while checking authentication
class AuthGuardLoadingScreen extends StatelessWidget {
  const AuthGuardLoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Checking authentication...',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Error screen shown when authentication check fails
class AuthGuardErrorScreen extends StatelessWidget {
  const AuthGuardErrorScreen({
    super.key,
    required this.error,
    this.onRetry,
  });

  final String error;
  final VoidCallback? onRetry;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 80,
                  color: theme.colorScheme.error,
                ),
                
                const SizedBox(height: 24),
                
                Text(
                  'Authentication Error',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                Text(
                  error,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 32),
                
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (onRetry != null) ...[
                      ElevatedButton.icon(
                        onPressed: onRetry,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Try Again'),
                      ),
                      const SizedBox(width: 16),
                    ],
                    OutlinedButton.icon(
                      onPressed: () {
                        Navigator.of(context).pushNamedAndRemoveUntil(
                          SignInScreen.routeName,
                          (route) => false,
                        );
                      },
                      icon: const Icon(Icons.login),
                      label: const Text('Sign In'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Route guard that checks authentication before navigation
class RouteGuard {
  RouteGuard._();

  /// Check if user can access the route
  static Future<bool> canAccess(
    String routeName,
    WidgetRef ref, {
    bool showDialog = true,
  }) async {
    // Check if route requires authentication
    if (!AppRouter.requiresAuth(routeName)) {
      return true;
    }

    // Check authentication state
    final authState = ref.read(authStateProvider);
    
    return authState.when(
      data: (user) => user != null,
      loading: () => false,
      error: (_, __) => false,
    );
  }

  /// Navigate to route with authentication check
  static Future<void> navigateTo(
    BuildContext context,
    WidgetRef ref,
    String routeName, {
    Object? arguments,
    bool replace = false,
  }) async {
    final canAccess = await RouteGuard.canAccess(routeName, ref);
    
    if (!canAccess) {
      // Redirect to sign in
      if (context.mounted) {
        Navigator.of(context).pushNamedAndRemoveUntil(
          SignInScreen.routeName,
          (route) => false,
        );
      }
      return;
    }

    // Navigate to the requested route
    if (context.mounted) {
      if (replace) {
        Navigator.of(context).pushReplacementNamed(
          routeName,
          arguments: arguments,
        );
      } else {
        Navigator.of(context).pushNamed(
          routeName,
          arguments: arguments,
        );
      }
    }
  }

  /// Show authentication required dialog
  static void showAuthRequiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Authentication Required'),
        content: const Text(
          'You need to sign in to access this feature.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushNamedAndRemoveUntil(
                SignInScreen.routeName,
                (route) => false,
              );
            },
            child: const Text('Sign In'),
          ),
        ],
      ),
    );
  }
}

/// Mixin for widgets that need authentication
mixin AuthRequiredMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthentication();
    });
  }

  void _checkAuthentication() {
    final authState = ref.read(authStateProvider);
    
    authState.when(
      data: (user) {
        if (user == null) {
          _redirectToSignIn();
        }
      },
      loading: () {
        // Wait for authentication check to complete
      },
      error: (_, __) {
        _redirectToSignIn();
      },
    );
  }

  void _redirectToSignIn() {
    if (mounted) {
      Navigator.of(context).pushNamedAndRemoveUntil(
        SignInScreen.routeName,
        (route) => false,
      );
    }
  }
}

/// Extension for easy authentication checks
extension AuthCheckExtension on WidgetRef {
  /// Check if user is authenticated
  bool get isAuthenticated {
    final authState = read(authStateProvider);
    return authState.when(
      data: (user) => user != null,
      loading: () => false,
      error: (_, __) => false,
    );
  }

  /// Get current user if authenticated
  dynamic get currentUser {
    final authState = read(authStateProvider);
    return authState.when(
      data: (user) => user,
      loading: () => null,
      error: (_, __) => null,
    );
  }
}
