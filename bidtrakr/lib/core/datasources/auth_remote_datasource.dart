import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../errors/exceptions.dart';
import '../models/auth_models.dart';

/// Abstract interface for authentication remote data source
abstract class AuthRemoteDataSource {
  /// Sign up with email and password
  Future<AuthUserModel> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  });

  /// Sign in with email and password
  Future<AuthUserModel> signInWithPassword({
    required String email,
    required String password,
  });

  /// Sign in with OTP (magic link)
  Future<void> signInWithOtp({
    required String email,
    String? redirectTo,
  });

  /// Verify OTP
  Future<AuthUserModel> verifyOtp({
    required String email,
    required String token,
    required OtpType type,
  });

  /// Sign out current user
  Future<void> signOut();

  /// Refresh current session
  Future<AuthUserModel> refreshSession();

  /// Reset password
  Future<void> resetPassword({
    required String email,
    String? redirectTo,
  });

  /// Update user profile
  Future<AuthUserModel> updateUser({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  });

  /// Get current user
  Future<AuthUserModel?> getCurrentUser();

  /// Get current session
  Future<SessionModel?> getCurrentSession();

  /// Listen to auth state changes
  Stream<AuthStateModel> get authStateChanges;
}

/// Implementation of authentication remote data source using Supabase
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  AuthRemoteDataSourceImpl({
    SupabaseClient? supabaseClient,
  }) : _supabaseClient = supabaseClient ?? SupabaseConfig.client;

  final SupabaseClient _supabaseClient;

  @override
  Future<AuthUserModel> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
        data: metadata,
      );

      if (response.user == null) {
        throw const AuthenticationException.invalidCredentials();
      }

      return AuthUserModel.fromSupabaseUser(
        response.user!,
        response.session,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('Sign up failed: $e');
    }
  }

  @override
  Future<AuthUserModel> signInWithPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw const AuthenticationException.invalidCredentials();
      }

      return AuthUserModel.fromSupabaseUser(
        response.user!,
        response.session,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('Sign in failed: $e');
    }
  }

  @override
  Future<void> signInWithOtp({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await _supabaseClient.auth.signInWithOtp(
        email: email,
        emailRedirectTo: redirectTo,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('OTP sign in failed: $e');
    }
  }

  @override
  Future<AuthUserModel> verifyOtp({
    required String email,
    required String token,
    required OtpType type,
  }) async {
    try {
      final response = await _supabaseClient.auth.verifyOTP(
        email: email,
        token: token,
        type: type,
      );

      if (response.user == null) {
        throw const AuthenticationException.invalidCredentials();
      }

      return AuthUserModel.fromSupabaseUser(
        response.user!,
        response.session,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('OTP verification failed: $e');
    }
  }

  @override
  Future<void> signOut() async {
    try {
      await _supabaseClient.auth.signOut();
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('Sign out failed: $e');
    }
  }

  @override
  Future<AuthUserModel> refreshSession() async {
    try {
      final response = await _supabaseClient.auth.refreshSession();

      if (response.user == null) {
        throw const AuthenticationException.sessionExpired();
      }

      return AuthUserModel.fromSupabaseUser(
        response.user!,
        response.session,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('Session refresh failed: $e');
    }
  }

  @override
  Future<void> resetPassword({
    required String email,
    String? redirectTo,
  }) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(
        email,
        redirectTo: redirectTo,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('Password reset failed: $e');
    }
  }

  @override
  Future<AuthUserModel> updateUser({
    String? email,
    String? password,
    Map<String, dynamic>? data,
  }) async {
    try {
      final response = await _supabaseClient.auth.updateUser(
        UserAttributes(
          email: email,
          password: password,
          data: data,
        ),
      );

      if (response.user == null) {
        throw const AuthenticationException.userNotFound();
      }

      return AuthUserModel.fromSupabaseUser(
        response.user!,
        response.session,
      );
    } on AuthException catch (e) {
      throw _mapAuthException(e);
    } catch (e) {
      throw NetworkException('User update failed: $e');
    }
  }

  @override
  Future<AuthUserModel?> getCurrentUser() async {
    try {
      final user = _supabaseClient.auth.currentUser;
      final session = _supabaseClient.auth.currentSession;

      if (user == null) return null;

      return AuthUserModel.fromSupabaseUser(user, session);
    } catch (e) {
      throw NetworkException('Failed to get current user: $e');
    }
  }

  @override
  Future<SessionModel?> getCurrentSession() async {
    try {
      final session = _supabaseClient.auth.currentSession;
      if (session == null) return null;

      return SessionModel.fromSupabaseSession(session);
    } catch (e) {
      throw NetworkException('Failed to get current session: $e');
    }
  }

  @override
  Stream<AuthStateModel> get authStateChanges {
    return _supabaseClient.auth.onAuthStateChange.map((data) {
      return AuthStateModel(
        event: data.event,
        session: data.session != null 
            ? SessionModel.fromSupabaseSession(data.session!)
            : null,
        user: data.session?.user != null
            ? AuthUserModel.fromSupabaseUser(data.session!.user, data.session)
            : null,
      );
    });
  }

  /// Map Supabase AuthException to custom exceptions
  AppException _mapAuthException(AuthException e) {
    switch (e.statusCode) {
      case '400':
        return const AuthenticationException.invalidCredentials();
      case '401':
        return const AuthenticationException.sessionExpired();
      case '404':
        return const AuthenticationException.userNotFound();
      case '422':
        return const AuthenticationException.emailAlreadyExists();
      case '429':
        return NetworkException('Too many requests: ${e.message}');
      default:
        return AuthenticationException(e.message);
    }
  }
}
