import 'dart:io';

import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:sqlite3_flutter_libs/sqlite3_flutter_libs.dart';

// Import all table definitions
part 'database.g.dart';

// Income table definition
class Income extends Table {
  TextColumn get uuid => text()();
  IntColumn get id => integer().nullable()();
  DateTimeColumn get date => dateTime()();
  IntColumn get initialMileage => integer()();
  IntColumn get finalMileage => integer()();
  RealColumn get initialGopay => real()();
  RealColumn get initialBca => real()();
  RealColumn get initialCash => real()();
  RealColumn get initialOvo => real()();
  RealColumn get initialBri => real()();
  RealColumn get initialRekpon => real()();
  RealColumn get finalGopay => real()();
  RealColumn get finalBca => real()();
  RealColumn get finalCash => real()();
  RealColumn get finalOvo => real()();
  RealColumn get finalBri => real()();
  RealColumn get finalRekpon => real()();
  RealColumn get initialCapital => real().nullable()();
  RealColumn get finalResult => real().nullable()();
  IntColumn get mileage => integer().nullable()();
  RealColumn get netIncome => real().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus =>
      text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};
}

// Orders table definition
class Orders extends Table {
  TextColumn get uuid => text()();
  IntColumn get id => integer().nullable()();
  DateTimeColumn get date => dateTime()();
  IntColumn get orderCompleted => integer()();
  IntColumn get orderMissed => integer()();
  IntColumn get orderCanceled => integer()();
  IntColumn get cbsOrder => integer()();
  IntColumn get incomingOrder => integer().nullable()();
  IntColumn get orderReceived => integer().nullable()();
  RealColumn get bidAcceptance => real().nullable()();
  RealColumn get tripCompletion => real().nullable()();
  IntColumn get points => integer()();
  RealColumn get trip => real()();
  RealColumn get bonus => real()();
  RealColumn get tips => real()();
  RealColumn get income => real().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus =>
      text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};
}

// Performance table definition
class Performance extends Table {
  TextColumn get uuid => text()();
  IntColumn get id => integer().nullable()();
  DateTimeColumn get date => dateTime()();
  RealColumn get bidPerformance => real()();
  RealColumn get tripPerformance => real()();
  IntColumn get activeDays => integer()();
  RealColumn get onlineHours => real()();
  RealColumn get avgCompleted => real().nullable()();
  RealColumn get avgOnline => real().nullable()();
  RealColumn get retention => real().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus =>
      text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};
}

// Spare Parts table definition
class SpareParts extends Table {
  TextColumn get uuid => text()();
  IntColumn get id => integer().nullable()();
  TextColumn get partName => text()();
  TextColumn get partType => text()();
  RealColumn get price => real()();
  IntColumn get mileageLimit => integer()();
  IntColumn get initialMileage => integer()();
  DateTimeColumn get installationDate =>
      dateTime().withDefault(currentDateAndTime)();
  IntColumn get currentMileage => integer().withDefault(const Constant(0))();
  BoolColumn get warningStatus =>
      boolean().withDefault(const Constant(false))();
  IntColumn get replacementCount => integer().withDefault(const Constant(0))();
  TextColumn get notes => text().withDefault(const Constant(''))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus =>
      text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};
}

// Spare Parts History table definition
class SparePartsHistory extends Table {
  TextColumn get uuid => text()();
  IntColumn get id => integer().nullable()();
  TextColumn get partName => text()();
  TextColumn get partType => text()();
  RealColumn get price => real()();
  DateTimeColumn get replacementDate => dateTime()();
  IntColumn get mileageAtReplacement => integer()();
  IntColumn get sparePartId => integer()();
  DateTimeColumn get installationDate => dateTime()();
  IntColumn get initialMileage => integer()();
  TextColumn get replacementReason =>
      text().withDefault(const Constant('Regular maintenance'))();
  IntColumn get replacedByPartId => integer().nullable()();
  IntColumn get replacementCount => integer().withDefault(const Constant(1))();
  IntColumn get usageDays => integer().withDefault(const Constant(0))();
  IntColumn get usageMileage => integer().withDefault(const Constant(0))();
  TextColumn get notes => text().withDefault(const Constant(''))();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get deletedAt => dateTime().nullable()();
  TextColumn get syncStatus =>
      text().withDefault(const Constant('pendingUpload'))();

  @override
  Set<Column> get primaryKey => {uuid};
}

// Level Settings table definition
class LevelSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  IntColumn get platinumPointsReq => integer()();
  RealColumn get platinumBidReq => real()();
  RealColumn get platinumTripReq => real()();
  IntColumn get goldPointsReq => integer()();
  RealColumn get goldBidReq => real()();
  RealColumn get goldTripReq => real()();
  IntColumn get silverPointsReq => integer()();
  RealColumn get silverBidReq => real()();
  RealColumn get silverTripReq => real()();
}

// App Settings table definition
class AppSettings extends Table {
  IntColumn get id => integer().autoIncrement()();
  DateTimeColumn get dateRangeStart => dateTime()();
  DateTimeColumn get dateRangeEnd => dateTime()();
  TextColumn get backupDirectoryPath => text().nullable()();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get lastSyncTime => dateTime().nullable()();
}

// Database class
@DriftDatabase(
  tables: [
    Income,
    Orders,
    Performance,
    SpareParts,
    SparePartsHistory,
    LevelSettings,
    AppSettings,
  ],
)
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 1;

  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) async {
      await m.createAll();

      // Insert default level settings
      await into(levelSettings).insert(
        LevelSettingsCompanion.insert(
          platinumPointsReq: 500,
          platinumBidReq: 0.95,
          platinumTripReq: 0.98,
          goldPointsReq: 350,
          goldBidReq: 0.90,
          goldTripReq: 0.95,
          silverPointsReq: 200,
          silverBidReq: 0.85,
          silverTripReq: 0.90,
        ),
      );

      // Insert default app settings
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      await into(appSettings).insert(
        AppSettingsCompanion.insert(
          dateRangeStart: startOfMonth,
          dateRangeEnd: endOfMonth,
        ),
      );
    },
  );
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'bidtrakr.db'));

    if (Platform.isAndroid) {
      await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
    }

    // Set up SQLite3 temp directory if needed
    // final cachebase = (await getTemporaryDirectory()).path;
    // sqlite3.tempDirectory = cachebase;

    return NativeDatabase.createInBackground(file);
  });
}
