import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import '../errors/exceptions.dart';
import '../models/auth_models.dart';

/// Abstract interface for authentication local data source
abstract class AuthLocalDataSource {
  /// Store authentication token securely
  Future<void> storeAuthToken(String token);

  /// Get stored authentication token
  Future<String?> getAuthToken();

  /// Remove authentication token
  Future<void> removeAuthToken();

  /// Store refresh token securely
  Future<void> storeRefreshToken(String token);

  /// Get stored refresh token
  Future<String?> getRefreshToken();

  /// Remove refresh token
  Future<void> removeRefreshToken();

  /// Store user data locally
  Future<void> storeUserData(AuthUserModel user);

  /// Get stored user data
  Future<AuthUserModel?> getUserData();

  /// Remove user data
  Future<void> removeUserData();

  /// Store session data
  Future<void> storeSessionData(SessionModel session);

  /// Get stored session data
  Future<SessionModel?> getSessionData();

  /// Remove session data
  Future<void> removeSessionData();

  /// Check if biometric authentication is enabled
  Future<bool> isBiometricEnabled();

  /// Enable/disable biometric authentication
  Future<void> setBiometricEnabled(bool enabled);

  /// Store user preferences
  Future<void> storeUserPreferences(Map<String, dynamic> preferences);

  /// Get user preferences
  Future<Map<String, dynamic>?> getUserPreferences();

  /// Clear all authentication data
  Future<void> clearAll();
}

/// Implementation of authentication local data source using secure storage
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  AuthLocalDataSourceImpl({
    FlutterSecureStorage? secureStorage,
  }) : _secureStorage = secureStorage ?? const FlutterSecureStorage(
          aOptions: AndroidOptions(
            encryptedSharedPreferences: true,
            keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
            storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
          ),
          iOptions: IOSOptions(
            accessibility: KeychainAccessibility.first_unlock_this_device,
            synchronizable: false,
          ),
        );

  final FlutterSecureStorage _secureStorage;

  // Storage keys
  static const String _authTokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';
  static const String _sessionDataKey = 'session_data';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _userPreferencesKey = 'user_preferences';

  @override
  Future<void> storeAuthToken(String token) async {
    try {
      await _secureStorage.write(key: _authTokenKey, value: token);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing auth token: $e');
      }
      throw FileException.writeFailed('Failed to store auth token');
    }
  }

  @override
  Future<String?> getAuthToken() async {
    try {
      return await _secureStorage.read(key: _authTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error reading auth token: $e');
      }
      throw FileException.readFailed('Failed to read auth token');
    }
  }

  @override
  Future<void> removeAuthToken() async {
    try {
      await _secureStorage.delete(key: _authTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing auth token: $e');
      }
      throw FileException.deleteFailed('Failed to remove auth token');
    }
  }

  @override
  Future<void> storeRefreshToken(String token) async {
    try {
      await _secureStorage.write(key: _refreshTokenKey, value: token);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing refresh token: $e');
      }
      throw FileException.writeFailed('Failed to store refresh token');
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await _secureStorage.read(key: _refreshTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error reading refresh token: $e');
      }
      throw FileException.readFailed('Failed to read refresh token');
    }
  }

  @override
  Future<void> removeRefreshToken() async {
    try {
      await _secureStorage.delete(key: _refreshTokenKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing refresh token: $e');
      }
      throw FileException.deleteFailed('Failed to remove refresh token');
    }
  }

  @override
  Future<void> storeUserData(AuthUserModel user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _secureStorage.write(key: _userDataKey, value: userJson);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing user data: $e');
      }
      throw FileException.writeFailed('Failed to store user data');
    }
  }

  @override
  Future<AuthUserModel?> getUserData() async {
    try {
      final userJson = await _secureStorage.read(key: _userDataKey);
      if (userJson == null) return null;

      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return AuthUserModel.fromJson(userMap);
    } catch (e) {
      if (kDebugMode) {
        print('Error reading user data: $e');
      }
      throw FileException.readFailed('Failed to read user data');
    }
  }

  @override
  Future<void> removeUserData() async {
    try {
      await _secureStorage.delete(key: _userDataKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing user data: $e');
      }
      throw FileException.deleteFailed('Failed to remove user data');
    }
  }

  @override
  Future<void> storeSessionData(SessionModel session) async {
    try {
      final sessionJson = jsonEncode(session.toJson());
      await _secureStorage.write(key: _sessionDataKey, value: sessionJson);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing session data: $e');
      }
      throw FileException.writeFailed('Failed to store session data');
    }
  }

  @override
  Future<SessionModel?> getSessionData() async {
    try {
      final sessionJson = await _secureStorage.read(key: _sessionDataKey);
      if (sessionJson == null) return null;

      final sessionMap = jsonDecode(sessionJson) as Map<String, dynamic>;
      return SessionModel.fromJson(sessionMap);
    } catch (e) {
      if (kDebugMode) {
        print('Error reading session data: $e');
      }
      throw FileException.readFailed('Failed to read session data');
    }
  }

  @override
  Future<void> removeSessionData() async {
    try {
      await _secureStorage.delete(key: _sessionDataKey);
    } catch (e) {
      if (kDebugMode) {
        print('Error removing session data: $e');
      }
      throw FileException.deleteFailed('Failed to remove session data');
    }
  }

  @override
  Future<bool> isBiometricEnabled() async {
    try {
      final value = await _secureStorage.read(key: _biometricEnabledKey);
      return value == 'true';
    } catch (e) {
      if (kDebugMode) {
        print('Error reading biometric setting: $e');
      }
      return false;
    }
  }

  @override
  Future<void> setBiometricEnabled(bool enabled) async {
    try {
      await _secureStorage.write(
        key: _biometricEnabledKey,
        value: enabled.toString(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error storing biometric setting: $e');
      }
      throw FileException.writeFailed('Failed to store biometric setting');
    }
  }

  @override
  Future<void> storeUserPreferences(Map<String, dynamic> preferences) async {
    try {
      final preferencesJson = jsonEncode(preferences);
      await _secureStorage.write(key: _userPreferencesKey, value: preferencesJson);
    } catch (e) {
      if (kDebugMode) {
        print('Error storing user preferences: $e');
      }
      throw FileException.writeFailed('Failed to store user preferences');
    }
  }

  @override
  Future<Map<String, dynamic>?> getUserPreferences() async {
    try {
      final preferencesJson = await _secureStorage.read(key: _userPreferencesKey);
      if (preferencesJson == null) return null;

      return jsonDecode(preferencesJson) as Map<String, dynamic>;
    } catch (e) {
      if (kDebugMode) {
        print('Error reading user preferences: $e');
      }
      throw FileException.readFailed('Failed to read user preferences');
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      await _secureStorage.deleteAll();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing all data: $e');
      }
      throw FileException.deleteFailed('Failed to clear all data');
    }
  }
}
