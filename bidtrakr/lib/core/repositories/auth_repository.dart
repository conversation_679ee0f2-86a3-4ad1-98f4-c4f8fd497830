import 'package:dartz/dartz.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';
import '../errors/failures.dart';
import '../errors/exceptions.dart';
import '../models/auth_models.dart';

/// Abstract interface for authentication repository
abstract class AuthRepository {
  /// Sign up with email and password
  Future<Either<Failure, AuthUserModel>> signUp(SignUpRequest request);

  /// Sign in with email and password
  Future<Either<Failure, AuthUserModel>> signInWithPassword(SignInRequest request);

  /// Sign in with OTP (magic link)
  Future<Either<Failure, void>> signInWithOtp(String email, {String? redirectTo});

  /// Verify OTP
  Future<Either<Failure, AuthUserModel>> verifyOtp(OtpVerificationRequest request);

  /// Sign out current user
  Future<Either<Failure, void>> signOut();

  /// Refresh current session
  Future<Either<Failure, AuthUserModel>> refreshSession();

  /// Reset password
  Future<Either<Failure, void>> resetPassword(PasswordResetRequest request);

  /// Update user profile
  Future<Either<Failure, AuthUserModel>> updateUser(UpdateUserRequest request);

  /// Get current user
  Future<Either<Failure, AuthUserModel?>> getCurrentUser();

  /// Get current session
  Future<Either<Failure, SessionModel?>> getCurrentSession();

  /// Check if user is authenticated
  Future<Either<Failure, bool>> isAuthenticated();

  /// Listen to auth state changes
  Stream<AuthStateModel> get authStateChanges;

  /// Enable/disable biometric authentication
  Future<Either<Failure, void>> setBiometricEnabled(bool enabled);

  /// Check if biometric authentication is enabled
  Future<Either<Failure, bool>> isBiometricEnabled();

  /// Store user preferences
  Future<Either<Failure, void>> storeUserPreferences(Map<String, dynamic> preferences);

  /// Get user preferences
  Future<Either<Failure, Map<String, dynamic>?>> getUserPreferences();

  /// Clear all authentication data
  Future<Either<Failure, void>> clearAllData();
}

/// Implementation of authentication repository
class AuthRepositoryImpl implements AuthRepository {
  AuthRepositoryImpl({
    required AuthRemoteDataSource remoteDataSource,
    required AuthLocalDataSource localDataSource,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource;

  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  @override
  Future<Either<Failure, AuthUserModel>> signUp(SignUpRequest request) async {
    try {
      // Validate request
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return Left(Failure.validation(validationErrors.first));
      }

      // Sign up with remote data source
      final user = await _remoteDataSource.signUp(
        email: request.email,
        password: request.password,
        metadata: {
          if (request.name != null) 'name': request.name,
          if (request.phone != null) 'phone': request.phone,
          ...request.metadata,
        },
      );

      // Store user data locally
      await _localDataSource.storeUserData(user);

      // Store session data if available
      if (user.session != null) {
        await _localDataSource.storeSessionData(user.session!);
        await _localDataSource.storeAuthToken(user.session!.accessToken);
        await _localDataSource.storeRefreshToken(user.session!.refreshToken);
      }

      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } on ValidationException catch (e) {
      return Left(Failure.validation(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Sign up failed: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUserModel>> signInWithPassword(SignInRequest request) async {
    try {
      // Validate request
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return Left(Failure.validation(validationErrors.first));
      }

      // Sign in with remote data source
      final user = await _remoteDataSource.signInWithPassword(
        email: request.email,
        password: request.password,
      );

      // Store user data locally
      await _localDataSource.storeUserData(user);

      // Store session data if available
      if (user.session != null) {
        await _localDataSource.storeSessionData(user.session!);
        await _localDataSource.storeAuthToken(user.session!.accessToken);
        await _localDataSource.storeRefreshToken(user.session!.refreshToken);
      }

      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } on ValidationException catch (e) {
      return Left(Failure.validation(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Sign in failed: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> signInWithOtp(String email, {String? redirectTo}) async {
    try {
      if (email.isEmpty) {
        return Left(Failure.validation('Email is required'));
      }

      await _remoteDataSource.signInWithOtp(
        email: email,
        redirectTo: redirectTo,
      );

      return const Right(null);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('OTP sign in failed: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUserModel>> verifyOtp(OtpVerificationRequest request) async {
    try {
      // Validate request
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return Left(Failure.validation(validationErrors.first));
      }

      // Verify OTP with remote data source
      final user = await _remoteDataSource.verifyOtp(
        email: request.email,
        token: request.token,
        type: request.type,
      );

      // Store user data locally
      await _localDataSource.storeUserData(user);

      // Store session data if available
      if (user.session != null) {
        await _localDataSource.storeSessionData(user.session!);
        await _localDataSource.storeAuthToken(user.session!.accessToken);
        await _localDataSource.storeRefreshToken(user.session!.refreshToken);
      }

      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } on ValidationException catch (e) {
      return Left(Failure.validation(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('OTP verification failed: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> signOut() async {
    try {
      // Sign out from remote
      await _remoteDataSource.signOut();

      // Clear local data
      await _localDataSource.clearAll();

      return const Right(null);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Sign out failed: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUserModel>> refreshSession() async {
    try {
      // Refresh session with remote data source
      final user = await _remoteDataSource.refreshSession();

      // Update local data
      await _localDataSource.storeUserData(user);

      // Update session data if available
      if (user.session != null) {
        await _localDataSource.storeSessionData(user.session!);
        await _localDataSource.storeAuthToken(user.session!.accessToken);
        await _localDataSource.storeRefreshToken(user.session!.refreshToken);
      }

      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Session refresh failed: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> resetPassword(PasswordResetRequest request) async {
    try {
      // Validate request
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return Left(Failure.validation(validationErrors.first));
      }

      await _remoteDataSource.resetPassword(
        email: request.email,
        redirectTo: request.redirectTo,
      );

      return const Right(null);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } on ValidationException catch (e) {
      return Left(Failure.validation(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Password reset failed: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUserModel>> updateUser(UpdateUserRequest request) async {
    try {
      // Validate request
      final validationErrors = request.validate();
      if (validationErrors.isNotEmpty) {
        return Left(Failure.validation(validationErrors.first));
      }

      // Update user with remote data source
      final user = await _remoteDataSource.updateUser(
        email: request.email,
        password: request.password,
        data: {
          if (request.name != null) 'name': request.name,
          if (request.phone != null) 'phone': request.phone,
          if (request.avatarUrl != null) 'avatar_url': request.avatarUrl,
          ...request.metadata,
        },
      );

      // Update local data
      await _localDataSource.storeUserData(user);

      return Right(user);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } on ValidationException catch (e) {
      return Left(Failure.validation(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('User update failed: $e'));
    }
  }

  @override
  Future<Either<Failure, AuthUserModel?>> getCurrentUser() async {
    try {
      // Try to get user from local storage first
      final localUser = await _localDataSource.getUserData();
      if (localUser != null) {
        return Right(localUser);
      }

      // If not found locally, try remote
      final remoteUser = await _remoteDataSource.getCurrentUser();
      if (remoteUser != null) {
        await _localDataSource.storeUserData(remoteUser);
      }

      return Right(remoteUser);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to get current user: $e'));
    }
  }

  @override
  Future<Either<Failure, SessionModel?>> getCurrentSession() async {
    try {
      // Try to get session from local storage first
      final localSession = await _localDataSource.getSessionData();
      if (localSession != null) {
        return Right(localSession);
      }

      // If not found locally, try remote
      final remoteSession = await _remoteDataSource.getCurrentSession();
      if (remoteSession != null) {
        await _localDataSource.storeSessionData(remoteSession);
      }

      return Right(remoteSession);
    } on AuthenticationException catch (e) {
      return Left(Failure.authentication(e.message, e.code));
    } on NetworkException catch (e) {
      return Left(Failure.network(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to get current session: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isAuthenticated() async {
    try {
      final userResult = await getCurrentUser();
      return userResult.fold(
        (failure) => Left(failure),
        (user) => Right(user != null),
      );
    } catch (e) {
      return Left(Failure.unknown('Failed to check authentication status: $e'));
    }
  }

  @override
  Stream<AuthStateModel> get authStateChanges {
    return _remoteDataSource.authStateChanges;
  }

  @override
  Future<Either<Failure, void>> setBiometricEnabled(bool enabled) async {
    try {
      await _localDataSource.setBiometricEnabled(enabled);
      return const Right(null);
    } on FileException catch (e) {
      return Left(Failure.file(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to set biometric setting: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isBiometricEnabled() async {
    try {
      final enabled = await _localDataSource.isBiometricEnabled();
      return Right(enabled);
    } on FileException catch (e) {
      return Left(Failure.file(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to get biometric setting: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> storeUserPreferences(Map<String, dynamic> preferences) async {
    try {
      await _localDataSource.storeUserPreferences(preferences);
      return const Right(null);
    } on FileException catch (e) {
      return Left(Failure.file(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to store user preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>?>> getUserPreferences() async {
    try {
      final preferences = await _localDataSource.getUserPreferences();
      return Right(preferences);
    } on FileException catch (e) {
      return Left(Failure.file(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to get user preferences: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> clearAllData() async {
    try {
      await _localDataSource.clearAll();
      return const Right(null);
    } on FileException catch (e) {
      return Left(Failure.file(e.message, e.code));
    } catch (e) {
      return Left(Failure.unknown('Failed to clear all data: $e'));
    }
  }
}
