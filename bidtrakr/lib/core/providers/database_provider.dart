import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:drift/drift.dart';
import '../datasources/database.dart';

/// Provider for the database path
/// This can be overridden in tests or for different environments
final databasePathProvider = Provider<String?>((ref) => null);

/// Provider for the main application database
/// This is a singleton that will be created once and reused throughout the app
final databaseProvider = Provider<AppDatabase>((ref) {
  return AppDatabase();
});

/// Provider for database initialization status
/// This tracks whether the database has been properly initialized
final databaseInitializationProvider = FutureProvider<bool>((ref) async {
  final database = ref.watch(databaseProvider);

  try {
    // Test database connection by performing a simple query
    await database.customSelect('SELECT 1').get();
    return true;
  } catch (e) {
    return false;
  }
});

/// Provider for database health check
/// This can be used to monitor database status throughout the app lifecycle
final databaseHealthProvider = StreamProvider<bool>((ref) async* {
  final database = ref.watch(databaseProvider);

  // Emit initial health status
  try {
    await database.customSelect('SELECT 1').get();
    yield true;
  } catch (e) {
    yield false;
  }

  // Periodically check database health (every 30 seconds)
  await for (final _ in Stream.periodic(const Duration(seconds: 30))) {
    try {
      await database.customSelect('SELECT 1').get();
      yield true;
    } catch (e) {
      yield false;
    }
  }
});

/// Provider for database statistics
/// This provides useful information about the database state
final databaseStatsProvider = FutureProvider<DatabaseStats>((ref) async {
  final database = ref.watch(databaseProvider);

  try {
    // Get record counts for each table
    final incomeCount = await database
        .customSelect(
          'SELECT COUNT(*) as count FROM income WHERE deleted_at IS NULL',
        )
        .getSingle();

    final ordersCount = await database
        .customSelect(
          'SELECT COUNT(*) as count FROM orders WHERE deleted_at IS NULL',
        )
        .getSingle();

    final performanceCount = await database
        .customSelect(
          'SELECT COUNT(*) as count FROM performance WHERE deleted_at IS NULL',
        )
        .getSingle();

    final sparePartsCount = await database
        .customSelect(
          'SELECT COUNT(*) as count FROM spare_parts WHERE deleted_at IS NULL',
        )
        .getSingle();

    final sparePartsHistoryCount = await database
        .customSelect(
          'SELECT COUNT(*) as count FROM spare_parts_history WHERE deleted_at IS NULL',
        )
        .getSingle();

    // Get database file size (approximate)
    final dbSizeResult = await database
        .customSelect('PRAGMA page_count')
        .getSingle();
    final pageSize = await database
        .customSelect('PRAGMA page_size')
        .getSingle();

    final dbSize =
        (dbSizeResult.data['page_count'] as int) *
        (pageSize.data['page_size'] as int);

    return DatabaseStats(
      incomeRecords: incomeCount.data['count'] as int,
      orderRecords: ordersCount.data['count'] as int,
      performanceRecords: performanceCount.data['count'] as int,
      sparePartsRecords: sparePartsCount.data['count'] as int,
      sparePartsHistoryRecords: sparePartsHistoryCount.data['count'] as int,
      databaseSizeBytes: dbSize,
      lastUpdated: DateTime.now(),
    );
  } catch (e) {
    return DatabaseStats.empty();
  }
});

/// Data class for database statistics
class DatabaseStats {
  const DatabaseStats({
    required this.incomeRecords,
    required this.orderRecords,
    required this.performanceRecords,
    required this.sparePartsRecords,
    required this.sparePartsHistoryRecords,
    required this.databaseSizeBytes,
    required this.lastUpdated,
  });

  final int incomeRecords;
  final int orderRecords;
  final int performanceRecords;
  final int sparePartsRecords;
  final int sparePartsHistoryRecords;
  final int databaseSizeBytes;
  final DateTime lastUpdated;

  /// Get total number of records across all tables
  int get totalRecords =>
      incomeRecords +
      orderRecords +
      performanceRecords +
      sparePartsRecords +
      sparePartsHistoryRecords;

  /// Get database size in MB
  double get databaseSizeMB => databaseSizeBytes / (1024 * 1024);

  /// Get database size in a human-readable format
  String get databaseSizeFormatted {
    if (databaseSizeBytes < 1024) {
      return '${databaseSizeBytes}B';
    } else if (databaseSizeBytes < 1024 * 1024) {
      return '${(databaseSizeBytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(databaseSizeBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// Create an empty stats object
  factory DatabaseStats.empty() {
    return DatabaseStats(
      incomeRecords: 0,
      orderRecords: 0,
      performanceRecords: 0,
      sparePartsRecords: 0,
      sparePartsHistoryRecords: 0,
      databaseSizeBytes: 0,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'DatabaseStats('
        'income: $incomeRecords, '
        'orders: $orderRecords, '
        'performance: $performanceRecords, '
        'spareParts: $sparePartsRecords, '
        'sparePartsHistory: $sparePartsHistoryRecords, '
        'size: $databaseSizeFormatted, '
        'updated: $lastUpdated'
        ')';
  }
}

/// Provider for database cleanup operations
/// This can be used to perform maintenance tasks
final databaseCleanupProvider = Provider<DatabaseCleanup>((ref) {
  final database = ref.watch(databaseProvider);
  return DatabaseCleanup(database);
});

/// Class for database cleanup operations
class DatabaseCleanup {
  const DatabaseCleanup(this.database);

  final AppDatabase database;

  /// Clean up soft-deleted records older than the specified duration
  Future<int> cleanupSoftDeletedRecords({
    Duration olderThan = const Duration(days: 30),
  }) async {
    final cutoffDate = DateTime.now().subtract(olderThan);
    int totalDeleted = 0;

    // Clean up income records
    final incomeDeleted = await database.customUpdate(
      'DELETE FROM income WHERE deleted_at IS NOT NULL AND deleted_at < ?',
      variables: [Variable.withDateTime(cutoffDate)],
    );
    totalDeleted += incomeDeleted;

    // Clean up order records
    final ordersDeleted = await database.customUpdate(
      'DELETE FROM orders WHERE deleted_at IS NOT NULL AND deleted_at < ?',
      variables: [Variable.withDateTime(cutoffDate)],
    );
    totalDeleted += ordersDeleted;

    // Clean up performance records
    final performanceDeleted = await database.customUpdate(
      'DELETE FROM performance WHERE deleted_at IS NOT NULL AND deleted_at < ?',
      variables: [Variable.withDateTime(cutoffDate)],
    );
    totalDeleted += performanceDeleted;

    // Clean up spare parts records
    final sparePartsDeleted = await database.customUpdate(
      'DELETE FROM spare_parts WHERE deleted_at IS NOT NULL AND deleted_at < ?',
      variables: [Variable.withDateTime(cutoffDate)],
    );
    totalDeleted += sparePartsDeleted;

    // Clean up spare parts history records
    final sparePartsHistoryDeleted = await database.customUpdate(
      'DELETE FROM spare_parts_history WHERE deleted_at IS NOT NULL AND deleted_at < ?',
      variables: [Variable.withDateTime(cutoffDate)],
    );
    totalDeleted += sparePartsHistoryDeleted;

    return totalDeleted;
  }

  /// Vacuum the database to reclaim space
  Future<void> vacuumDatabase() async {
    await database.customStatement('VACUUM');
  }

  /// Analyze the database to update statistics
  Future<void> analyzeDatabase() async {
    await database.customStatement('ANALYZE');
  }

  /// Perform full database maintenance
  Future<DatabaseMaintenanceResult> performMaintenance() async {
    final startTime = DateTime.now();

    // Clean up old soft-deleted records
    final deletedRecords = await cleanupSoftDeletedRecords();

    // Vacuum database
    await vacuumDatabase();

    // Analyze database
    await analyzeDatabase();

    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return DatabaseMaintenanceResult(
      deletedRecords: deletedRecords,
      duration: duration,
      completedAt: endTime,
    );
  }
}

/// Result of database maintenance operations
class DatabaseMaintenanceResult {
  const DatabaseMaintenanceResult({
    required this.deletedRecords,
    required this.duration,
    required this.completedAt,
  });

  final int deletedRecords;
  final Duration duration;
  final DateTime completedAt;

  @override
  String toString() {
    return 'DatabaseMaintenanceResult('
        'deleted: $deletedRecords records, '
        'duration: ${duration.inMilliseconds}ms, '
        'completed: $completedAt'
        ')';
  }
}
