import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/app_settings.dart';
import '../models/auth_models.dart';
import '../errors/failures.dart';
import '../datasources/auth_remote_datasource.dart';
import '../datasources/auth_local_datasource.dart';
import '../repositories/auth_repository.dart';
import '../services/auth_service.dart';
import '../services/session_service.dart';
import '../services/biometric_service.dart';
import '../services/security_service.dart';
import 'database_provider.dart';

/// Provider for the current theme mode
final themeModeProvider = StateNotifierProvider<ThemeModeNotifier, ThemeMode>((
  ref,
) {
  return ThemeModeNotifier();
});

/// Notifier for managing theme mode state
class ThemeModeNotifier extends StateNotifier<ThemeMode> {
  ThemeModeNotifier() : super(ThemeMode.system);

  /// Toggle between light and dark theme
  void toggleTheme() {
    state = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
  }

  /// Set specific theme mode
  void setThemeMode(ThemeMode mode) {
    state = mode;
  }

  /// Set theme to light mode
  void setLightTheme() {
    state = ThemeMode.light;
  }

  /// Set theme to dark mode
  void setDarkTheme() {
    state = ThemeMode.dark;
  }

  /// Set theme to system mode
  void setSystemTheme() {
    state = ThemeMode.system;
  }
}

/// Provider for the light theme data
final lightThemeProvider = Provider<ThemeData>((ref) {
  return AppTheme.lightTheme;
});

/// Provider for the dark theme data
final darkThemeProvider = Provider<ThemeData>((ref) {
  return AppTheme.darkTheme;
});

/// Provider for app settings
final appSettingsProvider =
    StateNotifierProvider<AppSettingsNotifier, AsyncValue<AppSettings>>((ref) {
      return AppSettingsNotifier();
    });

/// Notifier for managing app settings
class AppSettingsNotifier extends StateNotifier<AsyncValue<AppSettings>> {
  AppSettingsNotifier() : super(const AsyncValue.loading()) {
    _loadSettings();
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    try {
      // TODO: Load settings from database or shared preferences
      // For now, use default settings
      final settings = AppSettings.defaultSettings();
      state = AsyncValue.data(settings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.unknown('Failed to load app settings: $e'),
        stackTrace,
      );
    }
  }

  /// Update date range settings
  Future<void> updateDateRange(DateTime start, DateTime end) async {
    final currentSettings = state.valueOrNull;
    if (currentSettings == null) return;

    try {
      final updatedSettings = currentSettings.copyWith(
        dateRangeStart: start,
        dateRangeEnd: end,
        lastUpdated: DateTime.now(),
      );

      // TODO: Save to database
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.unknown('Failed to update date range: $e'),
        stackTrace,
      );
    }
  }

  /// Update backup directory path
  Future<void> updateBackupDirectory(String? path) async {
    final currentSettings = state.valueOrNull;
    if (currentSettings == null) return;

    try {
      final updatedSettings = currentSettings.copyWith(
        backupDirectoryPath: path,
        lastUpdated: DateTime.now(),
      );

      // TODO: Save to database
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.unknown('Failed to update backup directory: $e'),
        stackTrace,
      );
    }
  }

  /// Update last sync time
  Future<void> updateLastSyncTime(DateTime syncTime) async {
    final currentSettings = state.valueOrNull;
    if (currentSettings == null) return;

    try {
      final updatedSettings = currentSettings.copyWith(
        lastSyncTime: syncTime,
        lastUpdated: DateTime.now(),
      );

      // TODO: Save to database
      state = AsyncValue.data(updatedSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.unknown('Failed to update sync time: $e'),
        stackTrace,
      );
    }
  }

  /// Reset settings to defaults
  Future<void> resetToDefaults() async {
    try {
      final defaultSettings = AppSettings.defaultSettings();

      // TODO: Save to database
      state = AsyncValue.data(defaultSettings);
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.unknown('Failed to reset settings: $e'),
        stackTrace,
      );
    }
  }

  /// Refresh settings from storage
  Future<void> refresh() async {
    state = const AsyncValue.loading();
    await _loadSettings();
  }
}

// Authentication Data Sources
final authRemoteDataSourceProvider = Provider<AuthRemoteDataSource>((ref) {
  return AuthRemoteDataSourceImpl();
});

final authLocalDataSourceProvider = Provider<AuthLocalDataSource>((ref) {
  return AuthLocalDataSourceImpl();
});

// Authentication Repository
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(
    remoteDataSource: ref.watch(authRemoteDataSourceProvider),
    localDataSource: ref.watch(authLocalDataSourceProvider),
  );
});

// Authentication Service
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService(authRepository: ref.watch(authRepositoryProvider));
});

// Session Service
final sessionServiceProvider = Provider<SessionService>((ref) {
  return SessionService(authService: ref.watch(authServiceProvider));
});

// Biometric Service
final biometricServiceProvider = Provider<BiometricService>((ref) {
  return BiometricService();
});

// Security Service
final securityServiceProvider = Provider<SecurityService>((ref) {
  return SecurityService();
});

// Authentication State Provider
final authStateProvider =
    StateNotifierProvider<AuthStateNotifier, AsyncValue<AuthUserModel?>>((ref) {
      return AuthStateNotifier(
        authService: ref.watch(authServiceProvider),
        sessionService: ref.watch(sessionServiceProvider),
      );
    });

/// Notifier for managing authentication state
class AuthStateNotifier extends StateNotifier<AsyncValue<AuthUserModel?>> {
  AuthStateNotifier({
    required AuthService authService,
    required SessionService sessionService,
  }) : _authService = authService,
       _sessionService = sessionService,
       super(const AsyncValue.loading()) {
    _initialize();
  }

  final AuthService _authService;
  final SessionService _sessionService;

  /// Initialize authentication state
  Future<void> _initialize() async {
    try {
      // Initialize session service
      await _sessionService.initialize();

      // Check current authentication status
      await _checkAuthStatus();

      // Listen to auth state changes
      _authService.authStateChanges.listen((authState) {
        if (authState.isSignedIn && authState.user != null) {
          state = AsyncValue.data(authState.user!);
        } else {
          state = const AsyncValue.data(null);
        }
      });
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Failed to initialize auth: $e'),
        stackTrace,
      );
    }
  }

  /// Check current authentication status
  Future<void> _checkAuthStatus() async {
    try {
      final result = await _authService.getCurrentUser();
      result.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
        },
        (user) {
          state = AsyncValue.data(user);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Failed to check auth status: $e'),
        stackTrace,
      );
    }
  }

  /// Sign in with email and password
  Future<void> signInWithPassword(String email, String password) async {
    state = const AsyncValue.loading();

    try {
      final result = await _authService.signInWithPassword(
        email: email,
        password: password,
      );

      result.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
        },
        (user) {
          state = AsyncValue.data(user);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Sign in failed: $e'),
        stackTrace,
      );
    }
  }

  /// Sign up with email and password
  Future<void> signUp({
    required String email,
    required String password,
    String? name,
    String? phone,
  }) async {
    state = const AsyncValue.loading();

    try {
      final result = await _authService.signUp(
        email: email,
        password: password,
        name: name,
        phone: phone,
      );

      result.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
        },
        (user) {
          state = AsyncValue.data(user);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Sign up failed: $e'),
        stackTrace,
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      final result = await _authService.signOut();
      result.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
        },
        (_) {
          state = const AsyncValue.data(null);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Sign out failed: $e'),
        stackTrace,
      );
    }
  }

  /// Refresh authentication state
  Future<void> refresh() async {
    await _checkAuthStatus();
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      final result = await _authService.resetPassword(email: email);
      result.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
        },
        (_) {
          // Password reset email sent successfully
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Password reset failed: $e'),
        stackTrace,
      );
    }
  }

  /// Update user profile
  Future<void> updateProfile({
    String? email,
    String? name,
    String? phone,
    String? avatarUrl,
  }) async {
    try {
      final result = await _authService.updateProfile(
        email: email,
        name: name,
        phone: phone,
        avatarUrl: avatarUrl,
      );

      result.fold(
        (failure) {
          state = AsyncValue.error(failure, StackTrace.current);
        },
        (user) {
          state = AsyncValue.data(user);
        },
      );
    } catch (e, stackTrace) {
      state = AsyncValue.error(
        Failure.authentication('Profile update failed: $e'),
        stackTrace,
      );
    }
  }

  @override
  void dispose() {
    _sessionService.dispose();
    super.dispose();
  }
}

/// Provider for app initialization status
final appInitializationProvider = FutureProvider<bool>((ref) async {
  // Wait for database initialization
  final dbInitialized = await ref.watch(databaseInitializationProvider.future);

  // Wait for settings to load
  final settings = ref.watch(appSettingsProvider);
  final settingsLoaded = settings.hasValue;

  // Wait for auth state to be determined
  final authState = ref.watch(authStateProvider);
  final authStateLoaded = authState.hasValue;

  return dbInitialized && settingsLoaded && authStateLoaded;
});

/// Provider for app lifecycle state
final appLifecycleProvider =
    StateNotifierProvider<AppLifecycleNotifier, AppLifecycleState>((ref) {
      return AppLifecycleNotifier();
    });

/// Notifier for managing app lifecycle
class AppLifecycleNotifier extends StateNotifier<AppLifecycleState>
    with WidgetsBindingObserver {
  AppLifecycleNotifier() : super(AppLifecycleState.resumed) {
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    this.state = state;
  }
}

/// Provider for error reporting
final errorReportingProvider = Provider<ErrorReporting>((ref) {
  return ErrorReporting();
});

/// Error reporting service
class ErrorReporting {
  /// Report a non-fatal error
  void reportError(
    Object error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? context,
  }) {
    // TODO: Implement error reporting (e.g., Crashlytics, Sentry)
    debugPrint('Error reported: $error');
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }
    if (context != null) {
      debugPrint('Context: $context');
    }
  }

  /// Report a fatal error
  void reportFatalError(
    Object error,
    StackTrace? stackTrace, {
    Map<String, dynamic>? context,
  }) {
    // TODO: Implement fatal error reporting
    debugPrint('Fatal error reported: $error');
    if (stackTrace != null) {
      debugPrint('Stack trace: $stackTrace');
    }
    if (context != null) {
      debugPrint('Context: $context');
    }
  }

  /// Set user context for error reporting
  void setUserContext(String userId, {String? email, String? name}) {
    // TODO: Set user context in error reporting service
    debugPrint('User context set: $userId');
  }

  /// Clear user context
  void clearUserContext() {
    // TODO: Clear user context in error reporting service
    debugPrint('User context cleared');
  }
}
