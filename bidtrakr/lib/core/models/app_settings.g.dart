// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppSettingsImpl _$$AppSettingsImplFromJson(Map<String, dynamic> json) =>
    _$AppSettingsImpl(
      id: (json['id'] as num).toInt(),
      dateRangeStart: DateTime.parse(json['dateRangeStart'] as String),
      dateRangeEnd: DateTime.parse(json['dateRangeEnd'] as String),
      backupDirectoryPath: json['backupDirectoryPath'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      lastSyncTime: json['lastSyncTime'] == null
          ? null
          : DateTime.parse(json['lastSyncTime'] as String),
    );

Map<String, dynamic> _$$AppSettingsImplToJson(_$AppSettingsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'dateRangeStart': instance.dateRangeStart.toIso8601String(),
      'dateRangeEnd': instance.dateRangeEnd.toIso8601String(),
      'backupDirectoryPath': instance.backupDirectoryPath,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'lastSyncTime': instance.lastSyncTime?.toIso8601String(),
    };
