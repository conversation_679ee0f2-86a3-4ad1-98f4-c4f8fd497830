// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

AppSettings _$AppSettingsFromJson(Map<String, dynamic> json) {
  return _AppSettings.fromJson(json);
}

/// @nodoc
mixin _$AppSettings {
  int get id => throw _privateConstructorUsedError;
  DateTime get dateRangeStart => throw _privateConstructorUsedError;
  DateTime get dateRangeEnd => throw _privateConstructorUsedError;
  String? get backupDirectoryPath => throw _privateConstructorUsedError;
  DateTime get lastUpdated => throw _privateConstructorUsedError;
  DateTime? get lastSyncTime => throw _privateConstructorUsedError;

  /// Serializes this AppSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppSettingsCopyWith<AppSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppSettingsCopyWith<$Res> {
  factory $AppSettingsCopyWith(
    AppSettings value,
    $Res Function(AppSettings) then,
  ) = _$AppSettingsCopyWithImpl<$Res, AppSettings>;
  @useResult
  $Res call({
    int id,
    DateTime dateRangeStart,
    DateTime dateRangeEnd,
    String? backupDirectoryPath,
    DateTime lastUpdated,
    DateTime? lastSyncTime,
  });
}

/// @nodoc
class _$AppSettingsCopyWithImpl<$Res, $Val extends AppSettings>
    implements $AppSettingsCopyWith<$Res> {
  _$AppSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? dateRangeStart = null,
    Object? dateRangeEnd = null,
    Object? backupDirectoryPath = freezed,
    Object? lastUpdated = null,
    Object? lastSyncTime = freezed,
  }) {
    return _then(
      _value.copyWith(
            id: null == id
                ? _value.id
                : id // ignore: cast_nullable_to_non_nullable
                      as int,
            dateRangeStart: null == dateRangeStart
                ? _value.dateRangeStart
                : dateRangeStart // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            dateRangeEnd: null == dateRangeEnd
                ? _value.dateRangeEnd
                : dateRangeEnd // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            backupDirectoryPath: freezed == backupDirectoryPath
                ? _value.backupDirectoryPath
                : backupDirectoryPath // ignore: cast_nullable_to_non_nullable
                      as String?,
            lastUpdated: null == lastUpdated
                ? _value.lastUpdated
                : lastUpdated // ignore: cast_nullable_to_non_nullable
                      as DateTime,
            lastSyncTime: freezed == lastSyncTime
                ? _value.lastSyncTime
                : lastSyncTime // ignore: cast_nullable_to_non_nullable
                      as DateTime?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$AppSettingsImplCopyWith<$Res>
    implements $AppSettingsCopyWith<$Res> {
  factory _$$AppSettingsImplCopyWith(
    _$AppSettingsImpl value,
    $Res Function(_$AppSettingsImpl) then,
  ) = __$$AppSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({
    int id,
    DateTime dateRangeStart,
    DateTime dateRangeEnd,
    String? backupDirectoryPath,
    DateTime lastUpdated,
    DateTime? lastSyncTime,
  });
}

/// @nodoc
class __$$AppSettingsImplCopyWithImpl<$Res>
    extends _$AppSettingsCopyWithImpl<$Res, _$AppSettingsImpl>
    implements _$$AppSettingsImplCopyWith<$Res> {
  __$$AppSettingsImplCopyWithImpl(
    _$AppSettingsImpl _value,
    $Res Function(_$AppSettingsImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of AppSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? dateRangeStart = null,
    Object? dateRangeEnd = null,
    Object? backupDirectoryPath = freezed,
    Object? lastUpdated = null,
    Object? lastSyncTime = freezed,
  }) {
    return _then(
      _$AppSettingsImpl(
        id: null == id
            ? _value.id
            : id // ignore: cast_nullable_to_non_nullable
                  as int,
        dateRangeStart: null == dateRangeStart
            ? _value.dateRangeStart
            : dateRangeStart // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        dateRangeEnd: null == dateRangeEnd
            ? _value.dateRangeEnd
            : dateRangeEnd // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        backupDirectoryPath: freezed == backupDirectoryPath
            ? _value.backupDirectoryPath
            : backupDirectoryPath // ignore: cast_nullable_to_non_nullable
                  as String?,
        lastUpdated: null == lastUpdated
            ? _value.lastUpdated
            : lastUpdated // ignore: cast_nullable_to_non_nullable
                  as DateTime,
        lastSyncTime: freezed == lastSyncTime
            ? _value.lastSyncTime
            : lastSyncTime // ignore: cast_nullable_to_non_nullable
                  as DateTime?,
      ),
    );
  }
}

/// @nodoc
@JsonSerializable()
class _$AppSettingsImpl implements _AppSettings {
  const _$AppSettingsImpl({
    required this.id,
    required this.dateRangeStart,
    required this.dateRangeEnd,
    this.backupDirectoryPath,
    required this.lastUpdated,
    this.lastSyncTime,
  });

  factory _$AppSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppSettingsImplFromJson(json);

  @override
  final int id;
  @override
  final DateTime dateRangeStart;
  @override
  final DateTime dateRangeEnd;
  @override
  final String? backupDirectoryPath;
  @override
  final DateTime lastUpdated;
  @override
  final DateTime? lastSyncTime;

  @override
  String toString() {
    return 'AppSettings(id: $id, dateRangeStart: $dateRangeStart, dateRangeEnd: $dateRangeEnd, backupDirectoryPath: $backupDirectoryPath, lastUpdated: $lastUpdated, lastSyncTime: $lastSyncTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppSettingsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.dateRangeStart, dateRangeStart) ||
                other.dateRangeStart == dateRangeStart) &&
            (identical(other.dateRangeEnd, dateRangeEnd) ||
                other.dateRangeEnd == dateRangeEnd) &&
            (identical(other.backupDirectoryPath, backupDirectoryPath) ||
                other.backupDirectoryPath == backupDirectoryPath) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.lastSyncTime, lastSyncTime) ||
                other.lastSyncTime == lastSyncTime));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
    runtimeType,
    id,
    dateRangeStart,
    dateRangeEnd,
    backupDirectoryPath,
    lastUpdated,
    lastSyncTime,
  );

  /// Create a copy of AppSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppSettingsImplCopyWith<_$AppSettingsImpl> get copyWith =>
      __$$AppSettingsImplCopyWithImpl<_$AppSettingsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppSettingsImplToJson(this);
  }
}

abstract class _AppSettings implements AppSettings {
  const factory _AppSettings({
    required final int id,
    required final DateTime dateRangeStart,
    required final DateTime dateRangeEnd,
    final String? backupDirectoryPath,
    required final DateTime lastUpdated,
    final DateTime? lastSyncTime,
  }) = _$AppSettingsImpl;

  factory _AppSettings.fromJson(Map<String, dynamic> json) =
      _$AppSettingsImpl.fromJson;

  @override
  int get id;
  @override
  DateTime get dateRangeStart;
  @override
  DateTime get dateRangeEnd;
  @override
  String? get backupDirectoryPath;
  @override
  DateTime get lastUpdated;
  @override
  DateTime? get lastSyncTime;

  /// Create a copy of AppSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppSettingsImplCopyWith<_$AppSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
