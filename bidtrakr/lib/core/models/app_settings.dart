import 'package:freezed_annotation/freezed_annotation.dart';

part 'app_settings.freezed.dart';
part 'app_settings.g.dart';

/// Application settings model
@freezed
class AppSettings with _$AppSettings {
  const factory AppSettings({
    required int id,
    required DateTime dateRangeStart,
    required DateTime dateRangeEnd,
    String? backupDirectoryPath,
    required DateTime lastUpdated,
    DateTime? lastSyncTime,
  }) = _AppSettings;

  factory AppSettings.fromJson(Map<String, dynamic> json) =>
      _$AppSettingsFromJson(json);

  /// Create default settings
  factory AppSettings.defaultSettings() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    return AppSettings(
      id: 1,
      dateRangeStart: startOfMonth,
      dateRangeEnd: endOfMonth,
      backupDirectoryPath: null,
      lastUpdated: now,
      lastSyncTime: null,
    );
  }
}

/// Extension methods for AppSettings
extension AppSettingsExtension on AppSettings {
  /// Check if settings are valid
  bool get isValid {
    return dateRangeStart.isBefore(dateRangeEnd) ||
           dateRangeStart.isAtSameMomentAs(dateRangeEnd);
  }

  /// Get the duration of the date range
  Duration get dateRangeDuration {
    return dateRangeEnd.difference(dateRangeStart);
  }

  /// Get the number of days in the date range
  int get dateRangeDays {
    return dateRangeDuration.inDays + 1; // +1 to include both start and end dates
  }

  /// Check if a date is within the current date range
  bool isDateInRange(DateTime date) {
    final dateOnly = DateTime(date.year, date.month, date.day);
    final startOnly = DateTime(dateRangeStart.year, dateRangeStart.month, dateRangeStart.day);
    final endOnly = DateTime(dateRangeEnd.year, dateRangeEnd.month, dateRangeEnd.day);
    
    return (dateOnly.isAfter(startOnly) || dateOnly.isAtSameMomentAs(startOnly)) &&
           (dateOnly.isBefore(endOnly) || dateOnly.isAtSameMomentAs(endOnly));
  }

  /// Check if sync is needed (last sync was more than 1 hour ago)
  bool get needsSync {
    if (lastSyncTime == null) return true;
    
    final now = DateTime.now();
    final timeSinceLastSync = now.difference(lastSyncTime!);
    
    return timeSinceLastSync.inHours >= 1;
  }

  /// Get a human-readable description of the date range
  String get dateRangeDescription {
    final now = DateTime.now();
    final startOfThisMonth = DateTime(now.year, now.month, 1);
    final endOfThisMonth = DateTime(now.year, now.month + 1, 0);
    
    // Check if it's current month
    if (dateRangeStart.isAtSameMomentAs(startOfThisMonth) &&
        dateRangeEnd.isAtSameMomentAs(endOfThisMonth)) {
      return 'Current Month';
    }
    
    // Check if it's last month
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);
    
    if (dateRangeStart.isAtSameMomentAs(startOfLastMonth) &&
        dateRangeEnd.isAtSameMomentAs(endOfLastMonth)) {
      return 'Last Month';
    }
    
    // Check if it's current week
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    if (dateRangeStart.isAtSameMomentAs(DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day)) &&
        dateRangeEnd.isAtSameMomentAs(DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day))) {
      return 'Current Week';
    }
    
    // Check if it's last 7 days
    final sevenDaysAgo = now.subtract(const Duration(days: 6));
    if (dateRangeStart.isAtSameMomentAs(DateTime(sevenDaysAgo.year, sevenDaysAgo.month, sevenDaysAgo.day)) &&
        dateRangeEnd.isAtSameMomentAs(DateTime(now.year, now.month, now.day))) {
      return 'Last 7 Days';
    }
    
    // Check if it's last 30 days
    final thirtyDaysAgo = now.subtract(const Duration(days: 29));
    if (dateRangeStart.isAtSameMomentAs(DateTime(thirtyDaysAgo.year, thirtyDaysAgo.month, thirtyDaysAgo.day)) &&
        dateRangeEnd.isAtSameMomentAs(DateTime(now.year, now.month, now.day))) {
      return 'Last 30 Days';
    }
    
    // Custom range
    return 'Custom Range';
  }

  /// Get formatted date range string
  String get formattedDateRange {
    final startFormatted = '${dateRangeStart.day}/${dateRangeStart.month}/${dateRangeStart.year}';
    final endFormatted = '${dateRangeEnd.day}/${dateRangeEnd.month}/${dateRangeEnd.year}';
    
    return '$startFormatted - $endFormatted';
  }

  /// Check if backup directory is configured
  bool get hasBackupDirectory {
    return backupDirectoryPath != null && backupDirectoryPath!.isNotEmpty;
  }

  /// Get backup directory or default message
  String get backupDirectoryDisplay {
    return hasBackupDirectory ? backupDirectoryPath! : 'Not configured';
  }

  /// Create a copy with updated date range to current month
  AppSettings setCurrentMonth() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    
    return copyWith(
      dateRangeStart: startOfMonth,
      dateRangeEnd: endOfMonth,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a copy with updated date range to last month
  AppSettings setLastMonth() {
    final now = DateTime.now();
    final startOfLastMonth = DateTime(now.year, now.month - 1, 1);
    final endOfLastMonth = DateTime(now.year, now.month, 0);
    
    return copyWith(
      dateRangeStart: startOfLastMonth,
      dateRangeEnd: endOfLastMonth,
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a copy with updated date range to current week
  AppSettings setCurrentWeek() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    return copyWith(
      dateRangeStart: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      dateRangeEnd: DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day),
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a copy with updated date range to last 7 days
  AppSettings setLast7Days() {
    final now = DateTime.now();
    final sevenDaysAgo = now.subtract(const Duration(days: 6));
    
    return copyWith(
      dateRangeStart: DateTime(sevenDaysAgo.year, sevenDaysAgo.month, sevenDaysAgo.day),
      dateRangeEnd: DateTime(now.year, now.month, now.day),
      lastUpdated: DateTime.now(),
    );
  }

  /// Create a copy with updated date range to last 30 days
  AppSettings setLast30Days() {
    final now = DateTime.now();
    final thirtyDaysAgo = now.subtract(const Duration(days: 29));
    
    return copyWith(
      dateRangeStart: DateTime(thirtyDaysAgo.year, thirtyDaysAgo.month, thirtyDaysAgo.day),
      dateRangeEnd: DateTime(now.year, now.month, now.day),
      lastUpdated: DateTime.now(),
    );
  }
}

/// Predefined date range options
enum DateRangeOption {
  currentMonth,
  lastMonth,
  currentWeek,
  last7Days,
  last30Days,
  custom,
}

/// Extension for DateRangeOption
extension DateRangeOptionExtension on DateRangeOption {
  String get displayName {
    switch (this) {
      case DateRangeOption.currentMonth:
        return 'Current Month';
      case DateRangeOption.lastMonth:
        return 'Last Month';
      case DateRangeOption.currentWeek:
        return 'Current Week';
      case DateRangeOption.last7Days:
        return 'Last 7 Days';
      case DateRangeOption.last30Days:
        return 'Last 30 Days';
      case DateRangeOption.custom:
        return 'Custom Range';
    }
  }

  /// Get the date range for this option
  ({DateTime start, DateTime end}) get dateRange {
    final now = DateTime.now();
    
    switch (this) {
      case DateRangeOption.currentMonth:
        return (
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
      case DateRangeOption.lastMonth:
        return (
          start: DateTime(now.year, now.month - 1, 1),
          end: DateTime(now.year, now.month, 0),
        );
      case DateRangeOption.currentWeek:
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        final endOfWeek = startOfWeek.add(const Duration(days: 6));
        return (
          start: DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
          end: DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day),
        );
      case DateRangeOption.last7Days:
        final sevenDaysAgo = now.subtract(const Duration(days: 6));
        return (
          start: DateTime(sevenDaysAgo.year, sevenDaysAgo.month, sevenDaysAgo.day),
          end: DateTime(now.year, now.month, now.day),
        );
      case DateRangeOption.last30Days:
        final thirtyDaysAgo = now.subtract(const Duration(days: 29));
        return (
          start: DateTime(thirtyDaysAgo.year, thirtyDaysAgo.month, thirtyDaysAgo.day),
          end: DateTime(now.year, now.month, now.day),
        );
      case DateRangeOption.custom:
        // Return current month as default for custom
        return (
          start: DateTime(now.year, now.month, 1),
          end: DateTime(now.year, now.month + 1, 0),
        );
    }
  }
}
