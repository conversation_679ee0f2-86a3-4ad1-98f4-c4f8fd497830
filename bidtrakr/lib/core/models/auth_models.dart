import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

part 'auth_models.freezed.dart';
part 'auth_models.g.dart';

/// Authentication user model
@freezed
class AuthUserModel with _$AuthUserModel {
  const factory AuthUserModel({
    required String id,
    required String email,
    String? name,
    String? avatarUrl,
    String? phone,
    @Json<PERSON>ey(name: 'email_confirmed_at') DateTime? emailConfirmedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_confirmed_at') DateTime? phoneConfirmedAt,
    @Json<PERSON>ey(name: 'created_at') required DateTime createdAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at') required DateTime updatedAt,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'last_sign_in_at') DateTime? lastSignInAt,
    @Default({}) Map<String, dynamic> metadata,
    SessionModel? session,
  }) = _AuthUserModel;

  factory AuthUserModel.fromJson(Map<String, dynamic> json) =>
      _$AuthUserModelFromJson(json);

  /// Create from Supabase User
  factory AuthUserModel.fromSupabaseUser(User user, Session? session) {
    return AuthUserModel(
      id: user.id,
      email: user.email ?? '',
      name: user.userMetadata?['name'] as String?,
      avatarUrl: user.userMetadata?['avatar_url'] as String?,
      phone: user.phone,
      emailConfirmedAt: user.emailConfirmedAt,
      phoneConfirmedAt: user.phoneConfirmedAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastSignInAt: user.lastSignInAt,
      metadata: user.userMetadata ?? {},
      session: session != null
          ? SessionModel.fromSupabaseSession(session)
          : null,
    );
  }
}

/// Session model
@freezed
class SessionModel with _$SessionModel {
  const factory SessionModel({
    @JsonKey(name: 'access_token') required String accessToken,
    @JsonKey(name: 'refresh_token') required String refreshToken,
    @JsonKey(name: 'expires_in') required int expiresIn,
    @JsonKey(name: 'expires_at') required int expiresAt,
    @JsonKey(name: 'token_type') required String tokenType,
    required AuthUserModel user,
  }) = _SessionModel;

  factory SessionModel.fromJson(Map<String, dynamic> json) =>
      _$SessionModelFromJson(json);

  /// Create from Supabase Session
  factory SessionModel.fromSupabaseSession(Session session) {
    return SessionModel(
      accessToken: session.accessToken,
      refreshToken: session.refreshToken ?? '',
      expiresIn: session.expiresIn ?? 0,
      expiresAt: session.expiresAt ?? 0,
      tokenType: session.tokenType ?? 'bearer',
      user: AuthUserModel.fromSupabaseUser(session.user, null),
    );
  }
}

/// Authentication state model
@freezed
class AuthStateModel with _$AuthStateModel {
  const AuthStateModel._();

  const factory AuthStateModel({
    required AuthChangeEvent event,
    SessionModel? session,
    AuthUserModel? user,
  }) = _AuthStateModel;

  factory AuthStateModel.fromJson(Map<String, dynamic> json) =>
      _$AuthStateModelFromJson(json);

  /// Check if user is signed in
  bool get isSignedIn => session != null && user != null;

  /// Check if user is signed out
  bool get isSignedOut => session == null && user == null;

  /// Check if token was refreshed
  bool get isTokenRefreshed => event == AuthChangeEvent.tokenRefreshed;
}

/// Sign up request model
@freezed
class SignUpRequest with _$SignUpRequest {
  const factory SignUpRequest({
    required String email,
    required String password,
    String? name,
    String? phone,
    @Default({}) Map<String, dynamic> metadata,
  }) = _SignUpRequest;

  factory SignUpRequest.fromJson(Map<String, dynamic> json) =>
      _$SignUpRequestFromJson(json);

  /// Validate sign up request
  List<String> validate() {
    final errors = <String>[];

    if (email.isEmpty) {
      errors.add('Email is required');
    } else if (!_isValidEmail(email)) {
      errors.add('Please enter a valid email address');
    }

    if (password.isEmpty) {
      errors.add('Password is required');
    } else if (password.length < 8) {
      errors.add('Password must be at least 8 characters long');
    } else if (!_isStrongPassword(password)) {
      errors.add(
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      );
    }

    if (name != null && name!.trim().isEmpty) {
      errors.add('Name cannot be empty');
    }

    if (phone != null && phone!.isNotEmpty && !_isValidPhone(phone!)) {
      errors.add('Please enter a valid phone number');
    }

    return errors;
  }

  static bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  static bool _isStrongPassword(String password) {
    return RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
    ).hasMatch(password);
  }

  static bool _isValidPhone(String phone) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phone);
  }
}

/// Sign in request model
@freezed
class SignInRequest with _$SignInRequest {
  const factory SignInRequest({
    required String email,
    required String password,
  }) = _SignInRequest;

  factory SignInRequest.fromJson(Map<String, dynamic> json) =>
      _$SignInRequestFromJson(json);

  /// Validate sign in request
  List<String> validate() {
    final errors = <String>[];

    if (email.isEmpty) {
      errors.add('Email is required');
    } else if (!_isValidEmail(email)) {
      errors.add('Please enter a valid email address');
    }

    if (password.isEmpty) {
      errors.add('Password is required');
    }

    return errors;
  }

  static bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }
}

/// Password reset request model
@freezed
class PasswordResetRequest with _$PasswordResetRequest {
  const factory PasswordResetRequest({
    required String email,
    String? redirectTo,
  }) = _PasswordResetRequest;

  factory PasswordResetRequest.fromJson(Map<String, dynamic> json) =>
      _$PasswordResetRequestFromJson(json);

  /// Validate password reset request
  List<String> validate() {
    final errors = <String>[];

    if (email.isEmpty) {
      errors.add('Email is required');
    } else if (!_isValidEmail(email)) {
      errors.add('Please enter a valid email address');
    }

    return errors;
  }

  static bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }
}

/// Update user request model
@freezed
class UpdateUserRequest with _$UpdateUserRequest {
  const factory UpdateUserRequest({
    String? email,
    String? password,
    String? name,
    String? phone,
    String? avatarUrl,
    @Default({}) Map<String, dynamic> metadata,
  }) = _UpdateUserRequest;

  factory UpdateUserRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateUserRequestFromJson(json);

  /// Validate update user request
  List<String> validate() {
    final errors = <String>[];

    if (email != null && email!.isNotEmpty && !_isValidEmail(email!)) {
      errors.add('Please enter a valid email address');
    }

    if (password != null && password!.isNotEmpty) {
      if (password!.length < 8) {
        errors.add('Password must be at least 8 characters long');
      } else if (!_isStrongPassword(password!)) {
        errors.add(
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
        );
      }
    }

    if (name != null && name!.trim().isEmpty) {
      errors.add('Name cannot be empty');
    }

    if (phone != null && phone!.isNotEmpty && !_isValidPhone(phone!)) {
      errors.add('Please enter a valid phone number');
    }

    return errors;
  }

  static bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }

  static bool _isStrongPassword(String password) {
    return RegExp(
      r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]',
    ).hasMatch(password);
  }

  static bool _isValidPhone(String phone) {
    return RegExp(r'^\+?[1-9]\d{1,14}$').hasMatch(phone);
  }
}

/// OTP verification request model
@freezed
class OtpVerificationRequest with _$OtpVerificationRequest {
  const factory OtpVerificationRequest({
    required String email,
    required String token,
    required OtpType type,
  }) = _OtpVerificationRequest;

  factory OtpVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$OtpVerificationRequestFromJson(json);

  /// Validate OTP verification request
  List<String> validate() {
    final errors = <String>[];

    if (email.isEmpty) {
      errors.add('Email is required');
    } else if (!_isValidEmail(email)) {
      errors.add('Please enter a valid email address');
    }

    if (token.isEmpty) {
      errors.add('Verification code is required');
    } else if (token.length != 6) {
      errors.add('Verification code must be 6 digits');
    }

    return errors;
  }

  static bool _isValidEmail(String email) {
    return RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    ).hasMatch(email);
  }
}
