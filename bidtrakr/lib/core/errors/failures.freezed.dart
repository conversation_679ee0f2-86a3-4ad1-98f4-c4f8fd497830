// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
  'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models',
);

/// @nodoc
mixin _$Failure {
  String get message => throw _privateConstructorUsedError;
  String? get code => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FailureCopyWith<Failure> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FailureCopyWith<$Res> {
  factory $FailureCopyWith(Failure value, $Res Function(Failure) then) =
      _$FailureCopyWithImpl<$Res, Failure>;
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class _$FailureCopyWithImpl<$Res, $Val extends Failure>
    implements $FailureCopyWith<$Res> {
  _$FailureCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _value.copyWith(
            message: null == message
                ? _value.message
                : message // ignore: cast_nullable_to_non_nullable
                      as String,
            code: freezed == code
                ? _value.code
                : code // ignore: cast_nullable_to_non_nullable
                      as String?,
          )
          as $Val,
    );
  }
}

/// @nodoc
abstract class _$$DatabaseFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$DatabaseFailureImplCopyWith(
    _$DatabaseFailureImpl value,
    $Res Function(_$DatabaseFailureImpl) then,
  ) = __$$DatabaseFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$DatabaseFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$DatabaseFailureImpl>
    implements _$$DatabaseFailureImplCopyWith<$Res> {
  __$$DatabaseFailureImplCopyWithImpl(
    _$DatabaseFailureImpl _value,
    $Res Function(_$DatabaseFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$DatabaseFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$DatabaseFailureImpl implements DatabaseFailure {
  const _$DatabaseFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.database(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DatabaseFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DatabaseFailureImplCopyWith<_$DatabaseFailureImpl> get copyWith =>
      __$$DatabaseFailureImplCopyWithImpl<_$DatabaseFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return database(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return database?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return database(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return database?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (database != null) {
      return database(this);
    }
    return orElse();
  }
}

abstract class DatabaseFailure implements Failure {
  const factory DatabaseFailure(final String message, [final String? code]) =
      _$DatabaseFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DatabaseFailureImplCopyWith<_$DatabaseFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NetworkFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$NetworkFailureImplCopyWith(
    _$NetworkFailureImpl value,
    $Res Function(_$NetworkFailureImpl) then,
  ) = __$$NetworkFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$NetworkFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$NetworkFailureImpl>
    implements _$$NetworkFailureImplCopyWith<$Res> {
  __$$NetworkFailureImplCopyWithImpl(
    _$NetworkFailureImpl _value,
    $Res Function(_$NetworkFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$NetworkFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$NetworkFailureImpl implements NetworkFailure {
  const _$NetworkFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.network(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NetworkFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      __$$NetworkFailureImplCopyWithImpl<_$NetworkFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return network(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return network?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return network(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return network?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (network != null) {
      return network(this);
    }
    return orElse();
  }
}

abstract class NetworkFailure implements Failure {
  const factory NetworkFailure(final String message, [final String? code]) =
      _$NetworkFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NetworkFailureImplCopyWith<_$NetworkFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ValidationFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$ValidationFailureImplCopyWith(
    _$ValidationFailureImpl value,
    $Res Function(_$ValidationFailureImpl) then,
  ) = __$$ValidationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$ValidationFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$ValidationFailureImpl>
    implements _$$ValidationFailureImplCopyWith<$Res> {
  __$$ValidationFailureImplCopyWithImpl(
    _$ValidationFailureImpl _value,
    $Res Function(_$ValidationFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$ValidationFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$ValidationFailureImpl implements ValidationFailure {
  const _$ValidationFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.validation(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ValidationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      __$$ValidationFailureImplCopyWithImpl<_$ValidationFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return validation(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return validation?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return validation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return validation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (validation != null) {
      return validation(this);
    }
    return orElse();
  }
}

abstract class ValidationFailure implements Failure {
  const factory ValidationFailure(final String message, [final String? code]) =
      _$ValidationFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ValidationFailureImplCopyWith<_$ValidationFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$NotFoundFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$NotFoundFailureImplCopyWith(
    _$NotFoundFailureImpl value,
    $Res Function(_$NotFoundFailureImpl) then,
  ) = __$$NotFoundFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$NotFoundFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$NotFoundFailureImpl>
    implements _$$NotFoundFailureImplCopyWith<$Res> {
  __$$NotFoundFailureImplCopyWithImpl(
    _$NotFoundFailureImpl _value,
    $Res Function(_$NotFoundFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$NotFoundFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$NotFoundFailureImpl implements NotFoundFailure {
  const _$NotFoundFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.notFound(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NotFoundFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NotFoundFailureImplCopyWith<_$NotFoundFailureImpl> get copyWith =>
      __$$NotFoundFailureImplCopyWithImpl<_$NotFoundFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return notFound(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return notFound?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return notFound(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return notFound?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (notFound != null) {
      return notFound(this);
    }
    return orElse();
  }
}

abstract class NotFoundFailure implements Failure {
  const factory NotFoundFailure(final String message, [final String? code]) =
      _$NotFoundFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NotFoundFailureImplCopyWith<_$NotFoundFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BusinessLogicFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$BusinessLogicFailureImplCopyWith(
    _$BusinessLogicFailureImpl value,
    $Res Function(_$BusinessLogicFailureImpl) then,
  ) = __$$BusinessLogicFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$BusinessLogicFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$BusinessLogicFailureImpl>
    implements _$$BusinessLogicFailureImplCopyWith<$Res> {
  __$$BusinessLogicFailureImplCopyWithImpl(
    _$BusinessLogicFailureImpl _value,
    $Res Function(_$BusinessLogicFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$BusinessLogicFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$BusinessLogicFailureImpl implements BusinessLogicFailure {
  const _$BusinessLogicFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.businessLogic(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessLogicFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessLogicFailureImplCopyWith<_$BusinessLogicFailureImpl>
  get copyWith =>
      __$$BusinessLogicFailureImplCopyWithImpl<_$BusinessLogicFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return businessLogic(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return businessLogic?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (businessLogic != null) {
      return businessLogic(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return businessLogic(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return businessLogic?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (businessLogic != null) {
      return businessLogic(this);
    }
    return orElse();
  }
}

abstract class BusinessLogicFailure implements Failure {
  const factory BusinessLogicFailure(
    final String message, [
    final String? code,
  ]) = _$BusinessLogicFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessLogicFailureImplCopyWith<_$BusinessLogicFailureImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$AuthenticationFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$AuthenticationFailureImplCopyWith(
    _$AuthenticationFailureImpl value,
    $Res Function(_$AuthenticationFailureImpl) then,
  ) = __$$AuthenticationFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$AuthenticationFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$AuthenticationFailureImpl>
    implements _$$AuthenticationFailureImplCopyWith<$Res> {
  __$$AuthenticationFailureImplCopyWithImpl(
    _$AuthenticationFailureImpl _value,
    $Res Function(_$AuthenticationFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$AuthenticationFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$AuthenticationFailureImpl implements AuthenticationFailure {
  const _$AuthenticationFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.authentication(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AuthenticationFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AuthenticationFailureImplCopyWith<_$AuthenticationFailureImpl>
  get copyWith =>
      __$$AuthenticationFailureImplCopyWithImpl<_$AuthenticationFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return authentication(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return authentication?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return authentication(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return authentication?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (authentication != null) {
      return authentication(this);
    }
    return orElse();
  }
}

abstract class AuthenticationFailure implements Failure {
  const factory AuthenticationFailure(
    final String message, [
    final String? code,
  ]) = _$AuthenticationFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AuthenticationFailureImplCopyWith<_$AuthenticationFailureImpl>
  get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FileFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$FileFailureImplCopyWith(
    _$FileFailureImpl value,
    $Res Function(_$FileFailureImpl) then,
  ) = __$$FileFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$FileFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$FileFailureImpl>
    implements _$$FileFailureImplCopyWith<$Res> {
  __$$FileFailureImplCopyWithImpl(
    _$FileFailureImpl _value,
    $Res Function(_$FileFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$FileFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$FileFailureImpl implements FileFailure {
  const _$FileFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.file(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FileFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FileFailureImplCopyWith<_$FileFailureImpl> get copyWith =>
      __$$FileFailureImplCopyWithImpl<_$FileFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return file(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return file?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (file != null) {
      return file(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return file(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return file?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (file != null) {
      return file(this);
    }
    return orElse();
  }
}

abstract class FileFailure implements Failure {
  const factory FileFailure(final String message, [final String? code]) =
      _$FileFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FileFailureImplCopyWith<_$FileFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$UnknownFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$UnknownFailureImplCopyWith(
    _$UnknownFailureImpl value,
    $Res Function(_$UnknownFailureImpl) then,
  ) = __$$UnknownFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$UnknownFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$UnknownFailureImpl>
    implements _$$UnknownFailureImplCopyWith<$Res> {
  __$$UnknownFailureImplCopyWithImpl(
    _$UnknownFailureImpl _value,
    $Res Function(_$UnknownFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$UnknownFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$UnknownFailureImpl implements UnknownFailure {
  const _$UnknownFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.unknown(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnknownFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnknownFailureImplCopyWith<_$UnknownFailureImpl> get copyWith =>
      __$$UnknownFailureImplCopyWithImpl<_$UnknownFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return unknown(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return unknown?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return unknown(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return unknown?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (unknown != null) {
      return unknown(this);
    }
    return orElse();
  }
}

abstract class UnknownFailure implements Failure {
  const factory UnknownFailure(final String message, [final String? code]) =
      _$UnknownFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnknownFailureImplCopyWith<_$UnknownFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SyncFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$SyncFailureImplCopyWith(
    _$SyncFailureImpl value,
    $Res Function(_$SyncFailureImpl) then,
  ) = __$$SyncFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$SyncFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$SyncFailureImpl>
    implements _$$SyncFailureImplCopyWith<$Res> {
  __$$SyncFailureImplCopyWithImpl(
    _$SyncFailureImpl _value,
    $Res Function(_$SyncFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$SyncFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$SyncFailureImpl implements SyncFailure {
  const _$SyncFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.sync(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SyncFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SyncFailureImplCopyWith<_$SyncFailureImpl> get copyWith =>
      __$$SyncFailureImplCopyWithImpl<_$SyncFailureImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return sync(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return sync?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (sync != null) {
      return sync(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return sync(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return sync?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (sync != null) {
      return sync(this);
    }
    return orElse();
  }
}

abstract class SyncFailure implements Failure {
  const factory SyncFailure(final String message, [final String? code]) =
      _$SyncFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SyncFailureImplCopyWith<_$SyncFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PermissionFailureImplCopyWith<$Res>
    implements $FailureCopyWith<$Res> {
  factory _$$PermissionFailureImplCopyWith(
    _$PermissionFailureImpl value,
    $Res Function(_$PermissionFailureImpl) then,
  ) = __$$PermissionFailureImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String message, String? code});
}

/// @nodoc
class __$$PermissionFailureImplCopyWithImpl<$Res>
    extends _$FailureCopyWithImpl<$Res, _$PermissionFailureImpl>
    implements _$$PermissionFailureImplCopyWith<$Res> {
  __$$PermissionFailureImplCopyWithImpl(
    _$PermissionFailureImpl _value,
    $Res Function(_$PermissionFailureImpl) _then,
  ) : super(_value, _then);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? message = null, Object? code = freezed}) {
    return _then(
      _$PermissionFailureImpl(
        null == message
            ? _value.message
            : message // ignore: cast_nullable_to_non_nullable
                  as String,
        freezed == code
            ? _value.code
            : code // ignore: cast_nullable_to_non_nullable
                  as String?,
      ),
    );
  }
}

/// @nodoc

class _$PermissionFailureImpl implements PermissionFailure {
  const _$PermissionFailureImpl(this.message, [this.code]);

  @override
  final String message;
  @override
  final String? code;

  @override
  String toString() {
    return 'Failure.permission(message: $message, code: $code)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PermissionFailureImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.code, code) || other.code == code));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, code);

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      __$$PermissionFailureImplCopyWithImpl<_$PermissionFailureImpl>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(String message, String? code) database,
    required TResult Function(String message, String? code) network,
    required TResult Function(String message, String? code) validation,
    required TResult Function(String message, String? code) notFound,
    required TResult Function(String message, String? code) businessLogic,
    required TResult Function(String message, String? code) authentication,
    required TResult Function(String message, String? code) file,
    required TResult Function(String message, String? code) unknown,
    required TResult Function(String message, String? code) sync,
    required TResult Function(String message, String? code) permission,
  }) {
    return permission(message, code);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(String message, String? code)? database,
    TResult? Function(String message, String? code)? network,
    TResult? Function(String message, String? code)? validation,
    TResult? Function(String message, String? code)? notFound,
    TResult? Function(String message, String? code)? businessLogic,
    TResult? Function(String message, String? code)? authentication,
    TResult? Function(String message, String? code)? file,
    TResult? Function(String message, String? code)? unknown,
    TResult? Function(String message, String? code)? sync,
    TResult? Function(String message, String? code)? permission,
  }) {
    return permission?.call(message, code);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(String message, String? code)? database,
    TResult Function(String message, String? code)? network,
    TResult Function(String message, String? code)? validation,
    TResult Function(String message, String? code)? notFound,
    TResult Function(String message, String? code)? businessLogic,
    TResult Function(String message, String? code)? authentication,
    TResult Function(String message, String? code)? file,
    TResult Function(String message, String? code)? unknown,
    TResult Function(String message, String? code)? sync,
    TResult Function(String message, String? code)? permission,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(message, code);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(DatabaseFailure value) database,
    required TResult Function(NetworkFailure value) network,
    required TResult Function(ValidationFailure value) validation,
    required TResult Function(NotFoundFailure value) notFound,
    required TResult Function(BusinessLogicFailure value) businessLogic,
    required TResult Function(AuthenticationFailure value) authentication,
    required TResult Function(FileFailure value) file,
    required TResult Function(UnknownFailure value) unknown,
    required TResult Function(SyncFailure value) sync,
    required TResult Function(PermissionFailure value) permission,
  }) {
    return permission(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(DatabaseFailure value)? database,
    TResult? Function(NetworkFailure value)? network,
    TResult? Function(ValidationFailure value)? validation,
    TResult? Function(NotFoundFailure value)? notFound,
    TResult? Function(BusinessLogicFailure value)? businessLogic,
    TResult? Function(AuthenticationFailure value)? authentication,
    TResult? Function(FileFailure value)? file,
    TResult? Function(UnknownFailure value)? unknown,
    TResult? Function(SyncFailure value)? sync,
    TResult? Function(PermissionFailure value)? permission,
  }) {
    return permission?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(DatabaseFailure value)? database,
    TResult Function(NetworkFailure value)? network,
    TResult Function(ValidationFailure value)? validation,
    TResult Function(NotFoundFailure value)? notFound,
    TResult Function(BusinessLogicFailure value)? businessLogic,
    TResult Function(AuthenticationFailure value)? authentication,
    TResult Function(FileFailure value)? file,
    TResult Function(UnknownFailure value)? unknown,
    TResult Function(SyncFailure value)? sync,
    TResult Function(PermissionFailure value)? permission,
    required TResult orElse(),
  }) {
    if (permission != null) {
      return permission(this);
    }
    return orElse();
  }
}

abstract class PermissionFailure implements Failure {
  const factory PermissionFailure(final String message, [final String? code]) =
      _$PermissionFailureImpl;

  @override
  String get message;
  @override
  String? get code;

  /// Create a copy of Failure
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PermissionFailureImplCopyWith<_$PermissionFailureImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
mixin _$Result<T> {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T data) success,
    required TResult Function(Failure failure) failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T data)? success,
    TResult Function(Failure failure)? failure,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Success<T> value) success,
    required TResult Function(FailureResult<T> value) failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Success<T> value)? success,
    TResult? Function(FailureResult<T> value)? failure,
  }) => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Success<T> value)? success,
    TResult Function(FailureResult<T> value)? failure,
    required TResult orElse(),
  }) => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ResultCopyWith<T, $Res> {
  factory $ResultCopyWith(Result<T> value, $Res Function(Result<T>) then) =
      _$ResultCopyWithImpl<T, $Res, Result<T>>;
}

/// @nodoc
class _$ResultCopyWithImpl<T, $Res, $Val extends Result<T>>
    implements $ResultCopyWith<T, $Res> {
  _$ResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$SuccessImplCopyWith<T, $Res> {
  factory _$$SuccessImplCopyWith(
    _$SuccessImpl<T> value,
    $Res Function(_$SuccessImpl<T>) then,
  ) = __$$SuccessImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({T data});
}

/// @nodoc
class __$$SuccessImplCopyWithImpl<T, $Res>
    extends _$ResultCopyWithImpl<T, $Res, _$SuccessImpl<T>>
    implements _$$SuccessImplCopyWith<T, $Res> {
  __$$SuccessImplCopyWithImpl(
    _$SuccessImpl<T> _value,
    $Res Function(_$SuccessImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? data = freezed}) {
    return _then(
      _$SuccessImpl<T>(
        freezed == data
            ? _value.data
            : data // ignore: cast_nullable_to_non_nullable
                  as T,
      ),
    );
  }
}

/// @nodoc

class _$SuccessImpl<T> implements Success<T> {
  const _$SuccessImpl(this.data);

  @override
  final T data;

  @override
  String toString() {
    return 'Result<$T>.success(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SuccessImpl<T> &&
            const DeepCollectionEquality().equals(other.data, data));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(data));

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      __$$SuccessImplCopyWithImpl<T, _$SuccessImpl<T>>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T data) success,
    required TResult Function(Failure failure) failure,
  }) {
    return success(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? failure,
  }) {
    return success?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T data)? success,
    TResult Function(Failure failure)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Success<T> value) success,
    required TResult Function(FailureResult<T> value) failure,
  }) {
    return success(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Success<T> value)? success,
    TResult? Function(FailureResult<T> value)? failure,
  }) {
    return success?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Success<T> value)? success,
    TResult Function(FailureResult<T> value)? failure,
    required TResult orElse(),
  }) {
    if (success != null) {
      return success(this);
    }
    return orElse();
  }
}

abstract class Success<T> implements Result<T> {
  const factory Success(final T data) = _$SuccessImpl<T>;

  T get data;

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SuccessImplCopyWith<T, _$SuccessImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FailureResultImplCopyWith<T, $Res> {
  factory _$$FailureResultImplCopyWith(
    _$FailureResultImpl<T> value,
    $Res Function(_$FailureResultImpl<T>) then,
  ) = __$$FailureResultImplCopyWithImpl<T, $Res>;
  @useResult
  $Res call({Failure failure});

  $FailureCopyWith<$Res> get failure;
}

/// @nodoc
class __$$FailureResultImplCopyWithImpl<T, $Res>
    extends _$ResultCopyWithImpl<T, $Res, _$FailureResultImpl<T>>
    implements _$$FailureResultImplCopyWith<T, $Res> {
  __$$FailureResultImplCopyWithImpl(
    _$FailureResultImpl<T> _value,
    $Res Function(_$FailureResultImpl<T>) _then,
  ) : super(_value, _then);

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({Object? failure = null}) {
    return _then(
      _$FailureResultImpl<T>(
        null == failure
            ? _value.failure
            : failure // ignore: cast_nullable_to_non_nullable
                  as Failure,
      ),
    );
  }

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $FailureCopyWith<$Res> get failure {
    return $FailureCopyWith<$Res>(_value.failure, (value) {
      return _then(_value.copyWith(failure: value));
    });
  }
}

/// @nodoc

class _$FailureResultImpl<T> implements FailureResult<T> {
  const _$FailureResultImpl(this.failure);

  @override
  final Failure failure;

  @override
  String toString() {
    return 'Result<$T>.failure(failure: $failure)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FailureResultImpl<T> &&
            (identical(other.failure, failure) || other.failure == failure));
  }

  @override
  int get hashCode => Object.hash(runtimeType, failure);

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FailureResultImplCopyWith<T, _$FailureResultImpl<T>> get copyWith =>
      __$$FailureResultImplCopyWithImpl<T, _$FailureResultImpl<T>>(
        this,
        _$identity,
      );

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(T data) success,
    required TResult Function(Failure failure) failure,
  }) {
    return failure(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(T data)? success,
    TResult? Function(Failure failure)? failure,
  }) {
    return failure?.call(this.failure);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(T data)? success,
    TResult Function(Failure failure)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this.failure);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(Success<T> value) success,
    required TResult Function(FailureResult<T> value) failure,
  }) {
    return failure(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(Success<T> value)? success,
    TResult? Function(FailureResult<T> value)? failure,
  }) {
    return failure?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(Success<T> value)? success,
    TResult Function(FailureResult<T> value)? failure,
    required TResult orElse(),
  }) {
    if (failure != null) {
      return failure(this);
    }
    return orElse();
  }
}

abstract class FailureResult<T> implements Result<T> {
  const factory FailureResult(final Failure failure) = _$FailureResultImpl<T>;

  Failure get failure;

  /// Create a copy of Result
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FailureResultImplCopyWith<T, _$FailureResultImpl<T>> get copyWith =>
      throw _privateConstructorUsedError;
}
