import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// Base class for all failures in the application
/// Uses Freezed for immutable data classes and pattern matching
@freezed
class Failure with _$Failure {
  const factory Failure.database(String message, [String? code]) =
      DatabaseFailure;
  const factory Failure.network(String message, [String? code]) =
      NetworkFailure;
  const factory Failure.validation(String message, [String? code]) =
      ValidationFailure;
  const factory Failure.notFound(String message, [String? code]) =
      NotFoundFailure;
  const factory Failure.businessLogic(String message, [String? code]) =
      BusinessLogicFailure;
  const factory Failure.authentication(String message, [String? code]) =
      AuthenticationFailure;
  const factory Failure.file(String message, [String? code]) = FileFailure;
  const factory Failure.unknown(String message, [String? code]) =
      UnknownFailure;
  const factory Failure.sync(String message, [String? code]) = SyncFailure;
  const factory Failure.permission(String message, [String? code]) =
      PermissionFailure;
}

/// Extension to provide user-friendly error messages
extension FailureExtension on Failure {
  /// Get a user-friendly error message
  String get userMessage {
    return when(
      database: (message, code) => _getDatabaseUserMessage(code),
      network: (message, code) => _getNetworkUserMessage(code),
      validation: (message, code) =>
          message, // Validation messages are already user-friendly
      notFound: (message, code) => _getNotFoundUserMessage(code),
      businessLogic: (message, code) => _getBusinessLogicUserMessage(code),
      authentication: (message, code) => _getAuthenticationUserMessage(code),
      file: (message, code) => _getFileUserMessage(code),
      sync: (message, code) => _getSyncUserMessage(code),
      permission: (message, code) => _getPermissionUserMessage(code),
      unknown: (message, code) =>
          'An unexpected error occurred. Please try again.',
    );
  }

  /// Get the technical error message for logging
  String get technicalMessage {
    return when(
      database: (message, code) => message,
      network: (message, code) => message,
      validation: (message, code) => message,
      notFound: (message, code) => message,
      businessLogic: (message, code) => message,
      authentication: (message, code) => message,
      file: (message, code) => message,
      sync: (message, code) => message,
      permission: (message, code) => message,
      unknown: (message, code) => message,
    );
  }

  /// Get the error code if available
  String? get errorCode {
    return when(
      database: (message, code) => code,
      network: (message, code) => code,
      validation: (message, code) => code,
      notFound: (message, code) => code,
      businessLogic: (message, code) => code,
      authentication: (message, code) => code,
      file: (message, code) => code,
      sync: (message, code) => code,
      permission: (message, code) => code,
      unknown: (message, code) => code,
    );
  }

  /// Check if this is a critical error that should be reported
  bool get isCritical {
    return when(
      database: (message, code) =>
          code == 'DB_CORRUPTED_DATA' || code == 'DB_MIGRATION_FAILED',
      network: (message, code) => false, // Network errors are usually temporary
      validation: (message, code) => false, // Validation errors are user errors
      notFound: (message, code) =>
          false, // Not found errors are usually user errors
      businessLogic: (message, code) => code == 'BUSINESS_SYNC_CONFLICT',
      authentication: (message, code) => code == 'AUTH_ACCOUNT_DISABLED',
      file: (message, code) => code == 'FILE_CORRUPTED',
      sync: (message, code) => true, // Sync errors should be monitored
      permission: (message, code) =>
          false, // Permission errors are usually user errors
      unknown: (message, code) => true, // Unknown errors should be investigated
    );
  }

  /// Check if the error is recoverable
  bool get isRecoverable {
    return when(
      database: (message, code) => code != 'DB_CORRUPTED_DATA',
      network: (message, code) => true, // Network errors are usually temporary
      validation: (message, code) => true, // User can fix validation errors
      notFound: (message, code) => true, // User can create missing resources
      businessLogic: (message, code) => code != 'BUSINESS_BACKUP_CORRUPTED',
      authentication: (message, code) => code != 'AUTH_ACCOUNT_DISABLED',
      file: (message, code) => code != 'FILE_CORRUPTED',
      sync: (message, code) => true, // Sync errors can usually be resolved
      permission: (message, code) => true, // User can grant permissions
      unknown: (message, code) => false, // Unknown errors are unpredictable
    );
  }
}

// Private helper methods for user-friendly messages
String _getDatabaseUserMessage(String? code) {
  switch (code) {
    case 'DB_CONNECTION_FAILED':
      return 'Unable to access the database. Please restart the app.';
    case 'DB_CORRUPTED_DATA':
      return 'Database corruption detected. Please restore from backup.';
    case 'DB_MIGRATION_FAILED':
      return 'App update failed. Please reinstall the application.';
    default:
      return 'A database error occurred. Please try again.';
  }
}

String _getNetworkUserMessage(String? code) {
  switch (code) {
    case 'NETWORK_NO_CONNECTION':
      return 'No internet connection. Please check your network settings.';
    case 'NETWORK_TIMEOUT':
      return 'Request timed out. Please check your connection and try again.';
    case 'NETWORK_SERVER_ERROR':
      return 'Server is temporarily unavailable. Please try again later.';
    case 'NETWORK_UNAUTHORIZED':
      return 'Your session has expired. Please login again.';
    default:
      return 'Network error occurred. Please check your connection.';
  }
}

String _getNotFoundUserMessage(String? code) {
  switch (code) {
    case 'NOT_FOUND_RECORD':
      return 'The requested item could not be found.';
    case 'NOT_FOUND_FILE':
      return 'The requested file could not be found.';
    case 'NOT_FOUND_BACKUP':
      return 'Backup file not found. Please check the file location.';
    default:
      return 'The requested resource was not found.';
  }
}

String _getBusinessLogicUserMessage(String? code) {
  switch (code) {
    case 'BUSINESS_DUPLICATE_ENTRY':
      return 'This entry already exists. Please check your data.';
    case 'BUSINESS_SYNC_CONFLICT':
      return 'Data conflict detected. Please resolve conflicts and try again.';
    case 'BUSINESS_BACKUP_CORRUPTED':
      return 'Backup file is corrupted. Please use a different backup.';
    default:
      return 'Operation could not be completed. Please check your data.';
  }
}

String _getAuthenticationUserMessage(String? code) {
  switch (code) {
    case 'AUTH_INVALID_CREDENTIALS':
      return 'Invalid email or password. Please try again.';
    case 'AUTH_EMAIL_EXISTS':
      return 'An account with this email already exists.';
    case 'AUTH_SESSION_EXPIRED':
      return 'Your session has expired. Please login again.';
    case 'AUTH_ACCOUNT_DISABLED':
      return 'Your account has been disabled. Please contact support.';
    default:
      return 'Authentication failed. Please try again.';
  }
}

String _getFileUserMessage(String? code) {
  switch (code) {
    case 'FILE_PERMISSION_DENIED':
      return 'Permission denied. Please grant file access permissions.';
    case 'FILE_NOT_FOUND':
      return 'File not found. Please check the file location.';
    case 'FILE_CORRUPTED':
      return 'File is corrupted and cannot be read.';
    default:
      return 'File operation failed. Please try again.';
  }
}

String _getSyncUserMessage(String? code) {
  switch (code) {
    case 'SYNC_CONFLICT':
      return 'Sync conflict detected. Please resolve and try again.';
    case 'SYNC_FAILED':
      return 'Synchronization failed. Please check your connection.';
    default:
      return 'Sync error occurred. Please try again later.';
  }
}

String _getPermissionUserMessage(String? code) {
  switch (code) {
    case 'PERMISSION_DENIED':
      return 'Permission denied. Please grant the required permissions.';
    case 'PERMISSION_STORAGE':
      return 'Storage permission required. Please grant access in settings.';
    default:
      return 'Permission required. Please grant access to continue.';
  }
}

/// Result type for operations that can fail
/// This is an alternative to using Either from dartz
@freezed
class Result<T> with _$Result<T> {
  const factory Result.success(T data) = Success<T>;
  const factory Result.failure(Failure failure) = FailureResult<T>;
}

/// Extension methods for Result
extension ResultExtension<T> on Result<T> {
  /// Check if the result is successful
  bool get isSuccess => when(success: (_) => true, failure: (_) => false);

  /// Check if the result is a failure
  bool get isFailure => !isSuccess;

  /// Get the data if successful, null otherwise
  T? get dataOrNull => when(success: (data) => data, failure: (_) => null);

  /// Get the failure if failed, null otherwise
  Failure? get failureOrNull =>
      when(success: (_) => null, failure: (failure) => failure);

  /// Transform the success value
  Result<R> map<R>(R Function(T) transform) {
    return when(
      success: (data) => Result.success(transform(data)),
      failure: (failure) => Result.failure(failure),
    );
  }

  /// Transform the failure
  Result<T> mapFailure(Failure Function(Failure) transform) {
    return when(
      success: (data) => Result.success(data),
      failure: (failure) => Result.failure(transform(failure)),
    );
  }

  /// Execute different actions based on the result
  R fold<R>(
    R Function(T data) onSuccess,
    R Function(Failure failure) onFailure,
  ) {
    return when(success: onSuccess, failure: onFailure);
  }
}
