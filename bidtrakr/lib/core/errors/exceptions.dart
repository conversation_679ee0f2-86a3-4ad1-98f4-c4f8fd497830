/// Base exception class for all custom exceptions in the application
abstract class AppException implements Exception {
  const AppException(this.message, [this.code]);

  final String message;
  final String? code;

  @override
  String toString() => 'AppException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when database operations fail
class DatabaseException extends AppException {
  const DatabaseException(super.message, [super.code]);

  factory DatabaseException.connectionFailed() {
    return const DatabaseException(
      'Failed to connect to the database',
      'DB_CONNECTION_FAILED',
    );
  }

  factory DatabaseException.queryFailed(String query) {
    return DatabaseException(
      'Database query failed: $query',
      'DB_QUERY_FAILED',
    );
  }

  factory DatabaseException.insertFailed(String table) {
    return DatabaseException(
      'Failed to insert data into $table',
      'DB_INSERT_FAILED',
    );
  }

  factory DatabaseException.updateFailed(String table) {
    return DatabaseException(
      'Failed to update data in $table',
      'DB_UPDATE_FAILED',
    );
  }

  factory DatabaseException.deleteFailed(String table) {
    return DatabaseException(
      'Failed to delete data from $table',
      'DB_DELETE_FAILED',
    );
  }

  factory DatabaseException.migrationFailed() {
    return const DatabaseException(
      'Database migration failed',
      'DB_MIGRATION_FAILED',
    );
  }

  factory DatabaseException.corruptedData() {
    return const DatabaseException(
      'Database contains corrupted data',
      'DB_CORRUPTED_DATA',
    );
  }
}

/// Exception thrown when network operations fail
class NetworkException extends AppException {
  const NetworkException(super.message, [super.code]);

  factory NetworkException.noConnection() {
    return const NetworkException(
      'No internet connection available',
      'NETWORK_NO_CONNECTION',
    );
  }

  factory NetworkException.timeout() {
    return const NetworkException(
      'Network request timed out',
      'NETWORK_TIMEOUT',
    );
  }

  factory NetworkException.serverError(int statusCode) {
    return NetworkException(
      'Server error occurred (Status: $statusCode)',
      'NETWORK_SERVER_ERROR',
    );
  }

  factory NetworkException.badRequest() {
    return const NetworkException(
      'Bad request sent to server',
      'NETWORK_BAD_REQUEST',
    );
  }

  factory NetworkException.unauthorized() {
    return const NetworkException(
      'Unauthorized access - please login again',
      'NETWORK_UNAUTHORIZED',
    );
  }

  factory NetworkException.forbidden() {
    return const NetworkException(
      'Access forbidden - insufficient permissions',
      'NETWORK_FORBIDDEN',
    );
  }

  factory NetworkException.notFound() {
    return const NetworkException(
      'Requested resource not found',
      'NETWORK_NOT_FOUND',
    );
  }
}

/// Exception thrown when validation fails
class ValidationException extends AppException {
  const ValidationException(super.message, [super.code]);

  factory ValidationException.required(String field) {
    return ValidationException(
      '$field is required',
      'VALIDATION_REQUIRED',
    );
  }

  factory ValidationException.invalidFormat(String field) {
    return ValidationException(
      '$field has invalid format',
      'VALIDATION_INVALID_FORMAT',
    );
  }

  factory ValidationException.outOfRange(String field, String range) {
    return ValidationException(
      '$field is out of valid range: $range',
      'VALIDATION_OUT_OF_RANGE',
    );
  }

  factory ValidationException.tooShort(String field, int minLength) {
    return ValidationException(
      '$field must be at least $minLength characters long',
      'VALIDATION_TOO_SHORT',
    );
  }

  factory ValidationException.tooLong(String field, int maxLength) {
    return ValidationException(
      '$field must be no more than $maxLength characters long',
      'VALIDATION_TOO_LONG',
    );
  }

  factory ValidationException.invalidEmail() {
    return const ValidationException(
      'Please enter a valid email address',
      'VALIDATION_INVALID_EMAIL',
    );
  }

  factory ValidationException.passwordTooWeak() {
    return const ValidationException(
      'Password must contain at least 8 characters with uppercase, lowercase, and numbers',
      'VALIDATION_PASSWORD_WEAK',
    );
  }

  factory ValidationException.dateInFuture(String field) {
    return ValidationException(
      '$field cannot be in the future',
      'VALIDATION_DATE_FUTURE',
    );
  }

  factory ValidationException.dateInPast(String field) {
    return ValidationException(
      '$field cannot be in the past',
      'VALIDATION_DATE_PAST',
    );
  }

  factory ValidationException.negativeValue(String field) {
    return ValidationException(
      '$field cannot be negative',
      'VALIDATION_NEGATIVE_VALUE',
    );
  }
}

/// Exception thrown when a resource is not found
class NotFoundException extends AppException {
  const NotFoundException(super.message, [super.code]);

  factory NotFoundException.record(String type, String id) {
    return NotFoundException(
      '$type with ID $id not found',
      'NOT_FOUND_RECORD',
    );
  }

  factory NotFoundException.file(String path) {
    return NotFoundException(
      'File not found at path: $path',
      'NOT_FOUND_FILE',
    );
  }

  factory NotFoundException.user() {
    return const NotFoundException(
      'User not found or not authenticated',
      'NOT_FOUND_USER',
    );
  }

  factory NotFoundException.backup(String filename) {
    return NotFoundException(
      'Backup file not found: $filename',
      'NOT_FOUND_BACKUP',
    );
  }
}

/// Exception thrown when business logic rules are violated
class BusinessLogicException extends AppException {
  const BusinessLogicException(super.message, [super.code]);

  factory BusinessLogicException.duplicateEntry(String type) {
    return BusinessLogicException(
      'Duplicate $type entry already exists',
      'BUSINESS_DUPLICATE_ENTRY',
    );
  }

  factory BusinessLogicException.invalidOperation(String operation) {
    return BusinessLogicException(
      'Invalid operation: $operation',
      'BUSINESS_INVALID_OPERATION',
    );
  }

  factory BusinessLogicException.insufficientData(String requirement) {
    return BusinessLogicException(
      'Insufficient data: $requirement',
      'BUSINESS_INSUFFICIENT_DATA',
    );
  }

  factory BusinessLogicException.conflictingData(String conflict) {
    return BusinessLogicException(
      'Conflicting data detected: $conflict',
      'BUSINESS_CONFLICTING_DATA',
    );
  }

  factory BusinessLogicException.operationNotAllowed(String reason) {
    return BusinessLogicException(
      'Operation not allowed: $reason',
      'BUSINESS_OPERATION_NOT_ALLOWED',
    );
  }

  factory BusinessLogicException.syncConflict() {
    return const BusinessLogicException(
      'Data synchronization conflict detected',
      'BUSINESS_SYNC_CONFLICT',
    );
  }

  factory BusinessLogicException.backupCorrupted() {
    return const BusinessLogicException(
      'Backup file is corrupted or invalid',
      'BUSINESS_BACKUP_CORRUPTED',
    );
  }

  factory BusinessLogicException.levelRequirementsNotMet() {
    return const BusinessLogicException(
      'Level requirements not met for promotion',
      'BUSINESS_LEVEL_REQUIREMENTS_NOT_MET',
    );
  }
}

/// Exception thrown when authentication fails
class AuthenticationException extends AppException {
  const AuthenticationException(super.message, [super.code]);

  factory AuthenticationException.invalidCredentials() {
    return const AuthenticationException(
      'Invalid email or password',
      'AUTH_INVALID_CREDENTIALS',
    );
  }

  factory AuthenticationException.userNotFound() {
    return const AuthenticationException(
      'User account not found',
      'AUTH_USER_NOT_FOUND',
    );
  }

  factory AuthenticationException.emailAlreadyExists() {
    return const AuthenticationException(
      'An account with this email already exists',
      'AUTH_EMAIL_EXISTS',
    );
  }

  factory AuthenticationException.sessionExpired() {
    return const AuthenticationException(
      'Your session has expired. Please login again',
      'AUTH_SESSION_EXPIRED',
    );
  }

  factory AuthenticationException.emailNotVerified() {
    return const AuthenticationException(
      'Please verify your email address before continuing',
      'AUTH_EMAIL_NOT_VERIFIED',
    );
  }

  factory AuthenticationException.accountDisabled() {
    return const AuthenticationException(
      'Your account has been disabled. Please contact support',
      'AUTH_ACCOUNT_DISABLED',
    );
  }
}

/// Exception thrown when file operations fail
class FileException extends AppException {
  const FileException(super.message, [super.code]);

  factory FileException.readFailed(String path) {
    return FileException(
      'Failed to read file: $path',
      'FILE_READ_FAILED',
    );
  }

  factory FileException.writeFailed(String path) {
    return FileException(
      'Failed to write file: $path',
      'FILE_WRITE_FAILED',
    );
  }

  factory FileException.deleteFailed(String path) {
    return FileException(
      'Failed to delete file: $path',
      'FILE_DELETE_FAILED',
    );
  }

  factory FileException.permissionDenied(String path) {
    return FileException(
      'Permission denied for file: $path',
      'FILE_PERMISSION_DENIED',
    );
  }

  factory FileException.notFound(String path) {
    return FileException(
      'File not found: $path',
      'FILE_NOT_FOUND',
    );
  }

  factory FileException.invalidFormat(String expectedFormat) {
    return FileException(
      'Invalid file format. Expected: $expectedFormat',
      'FILE_INVALID_FORMAT',
    );
  }

  factory FileException.corruptedFile(String path) {
    return FileException(
      'File is corrupted: $path',
      'FILE_CORRUPTED',
    );
  }
}
