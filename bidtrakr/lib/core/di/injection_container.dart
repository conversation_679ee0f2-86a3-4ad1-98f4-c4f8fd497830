import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../datasources/database.dart';
import '../providers/database_provider.dart';
import '../providers/app_providers.dart';

/// Dependency injection container using Riverpod providers
/// This file centralizes all provider definitions for easy management and testing
class InjectionContainer {
  InjectionContainer._();

  /// Initialize all dependencies
  static void init() {
    // Dependencies are automatically initialized when providers are first accessed
    // This method can be used for any additional setup if needed
  }
}

/// Core infrastructure providers
final coreProvidersContainer = [
  // Database providers
  databaseProvider,
  databaseInitializationProvider,
  databaseHealthProvider,
  databaseStatsProvider,
  databaseCleanupProvider,

  // App state providers
  themeModeProvider,
  lightThemeProvider,
  darkThemeProvider,
  appSettingsProvider,
  authStateProvider,
  appInitializationProvider,
  appLifecycleProvider,
  errorReportingProvider,
];

/// Repository providers container
/// These will be added as repositories are implemented
final repositoryProvidersContainer = <Provider>[];

/// Service providers container
/// These will be added as services are implemented
final serviceProvidersContainer = <Provider>[];

/// Use case providers container
/// These will be added as use cases are implemented
final useCaseProvidersContainer = <Provider>[];

/// Feature providers container
/// These will be added as features are implemented
final featureProvidersContainer = <Provider>[];

/// All providers combined for easy access
final allProviders = [
  ...coreProvidersContainer,
  ...repositoryProvidersContainer,
  ...serviceProvidersContainer,
  ...useCaseProvidersContainer,
  ...featureProvidersContainer,
];

/// Provider overrides for testing
/// This allows us to override providers with mock implementations during testing
class TestProviderOverrides {
  static List<Override> get overrides => [
    // Database overrides for testing
    databaseProvider.overrideWith((ref) => MockAppDatabase()),

    // Auth state overrides for testing
    authStateProvider.overrideWith((ref) => MockAuthStateNotifier()),

    // Settings overrides for testing
    appSettingsProvider.overrideWith((ref) => MockAppSettingsNotifier()),
  ];
}

/// Mock implementations for testing
class MockAppDatabase extends AppDatabase {
  // Mock implementation will be added when needed for testing
}

class MockAuthStateNotifier extends AuthStateNotifier {
  // Mock implementation will be added when needed for testing
}

class MockAppSettingsNotifier extends AppSettingsNotifier {
  // Mock implementation will be added when needed for testing
}

/// Provider scope for different environments
enum ProviderScope { production, development, testing }

/// Environment-specific provider configurations
class EnvironmentProviders {
  static List<Override> getOverrides(ProviderScope scope) {
    switch (scope) {
      case ProviderScope.production:
        return [];
      case ProviderScope.development:
        return [
          // Development-specific overrides
          // For example, different database path or debug settings
        ];
      case ProviderScope.testing:
        return TestProviderOverrides.overrides;
    }
  }
}

/// Provider container for feature-specific dependencies
/// This allows features to register their own providers
class FeatureProviderContainer {
  static final Map<String, List<Provider>> _featureProviders = {};

  /// Register providers for a specific feature
  static void registerFeature(String featureName, List<Provider> providers) {
    _featureProviders[featureName] = providers;
  }

  /// Get providers for a specific feature
  static List<Provider> getFeatureProviders(String featureName) {
    return _featureProviders[featureName] ?? [];
  }

  /// Get all feature providers
  static List<Provider> getAllFeatureProviders() {
    return _featureProviders.values.expand((providers) => providers).toList();
  }

  /// Clear all feature providers (useful for testing)
  static void clear() {
    _featureProviders.clear();
  }
}

/// Dependency injection helper for manual dependency resolution
/// This can be used when providers are not available (e.g., in background tasks)
class DIHelper {
  static final Map<Type, dynamic> _instances = {};

  /// Register a singleton instance
  static void registerSingleton<T>(T instance) {
    _instances[T] = instance;
  }

  /// Get a registered instance
  static T get<T>() {
    final instance = _instances[T];
    if (instance == null) {
      throw Exception('Instance of type $T not registered');
    }
    return instance as T;
  }

  /// Check if an instance is registered
  static bool isRegistered<T>() {
    return _instances.containsKey(T);
  }

  /// Clear all registered instances
  static void clear() {
    _instances.clear();
  }

  /// Initialize core dependencies manually
  static void initCore() {
    // Register core dependencies that might be needed outside of the widget tree
    registerSingleton<AppDatabase>(AppDatabase());
  }
}

/// Provider observer for debugging and monitoring
class AppProviderObserver extends ProviderObserver {
  @override
  void didAddProvider(
    ProviderBase<Object?> provider,
    Object? value,
    ProviderContainer container,
  ) {
    super.didAddProvider(provider, value, container);
    // Log provider creation in debug mode
    // debugPrint('Provider added: ${provider.name ?? provider.runtimeType}');
  }

  @override
  void didDisposeProvider(
    ProviderBase<Object?> provider,
    ProviderContainer container,
  ) {
    super.didDisposeProvider(provider, container);
    // Log provider disposal in debug mode
    // debugPrint('Provider disposed: ${provider.name ?? provider.runtimeType}');
  }

  @override
  void didUpdateProvider(
    ProviderBase<Object?> provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    super.didUpdateProvider(provider, previousValue, newValue, container);
    // Log provider updates in debug mode
    // debugPrint('Provider updated: ${provider.name ?? provider.runtimeType}');
  }

  @override
  void providerDidFail(
    ProviderBase<Object?> provider,
    Object error,
    StackTrace stackTrace,
    ProviderContainer container,
  ) {
    super.providerDidFail(provider, error, stackTrace, container);
    // Log provider failures
    // debugPrint('Provider failed: ${provider.name ?? provider.runtimeType} - $error');
  }
}

/// Utility class for provider management
class ProviderUtils {
  /// Safely read a provider value
  static T? safeRead<T>(WidgetRef ref, ProviderBase<T> provider) {
    try {
      return ref.read(provider);
    } catch (e) {
      // Log error and return null
      // debugPrint('Error reading provider ${provider.runtimeType}: $e');
      return null;
    }
  }

  /// Safely watch a provider value
  static T? safeWatch<T>(WidgetRef ref, ProviderBase<T> provider) {
    try {
      return ref.watch(provider);
    } catch (e) {
      // Log error and return null
      // debugPrint('Error watching provider ${provider.runtimeType}: $e');
      return null;
    }
  }

  /// Invalidate a provider safely
  static void safeInvalidate(WidgetRef ref, ProviderBase provider) {
    try {
      ref.invalidate(provider);
    } catch (e) {
      // Log error
      // debugPrint('Error invalidating provider ${provider.runtimeType}: $e');
    }
  }

  /// Refresh a provider safely
  static Future<void> safeRefresh<T>(
    WidgetRef ref,
    Refreshable<T> provider,
  ) async {
    try {
      await ref.refresh(provider);
    } catch (e) {
      // Log error
      // debugPrint('Error refreshing provider ${provider.runtimeType}: $e');
    }
  }
}
