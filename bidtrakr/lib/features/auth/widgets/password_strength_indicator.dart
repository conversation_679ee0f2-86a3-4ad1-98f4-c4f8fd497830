import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/security_service.dart';

/// Widget to display password strength indicator
class PasswordStrengthIndicator extends ConsumerWidget {
  const PasswordStrengthIndicator({
    super.key,
    required this.password,
  });

  final String password;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (password.isEmpty) return const SizedBox.shrink();

    final securityService = ref.read(securityServiceProvider);
    final strength = securityService.checkPasswordStrength(password);
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Strength bar
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: strength.percentage,
                backgroundColor: theme.colorScheme.surfaceVariant,
                valueColor: AlwaysStoppedAnimation<Color>(
                  _getStrengthColor(strength, theme),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              strength.description,
              style: theme.textTheme.bodySmall?.copyWith(
                color: _getStrengthColor(strength, theme),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        
        if (strength.missingRequirements.isNotEmpty) ...[
          const SizedBox(height: 8),
          ...strength.missingRequirements.map((requirement) => Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: Row(
              children: [
                Icon(
                  Icons.close,
                  size: 12,
                  color: theme.colorScheme.error,
                ),
                const SizedBox(width: 4),
                Text(
                  requirement,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.error,
                  ),
                ),
              ],
            ),
          )),
        ],
      ],
    );
  }

  Color _getStrengthColor(PasswordStrength strength, ThemeData theme) {
    if (strength.isStrong) return Colors.green;
    if (strength.isGood) return Colors.lightGreen;
    if (strength.isFair) return Colors.orange;
    return theme.colorScheme.error;
  }
}
