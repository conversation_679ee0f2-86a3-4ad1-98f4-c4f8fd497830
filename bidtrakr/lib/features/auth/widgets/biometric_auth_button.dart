import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/providers/app_providers.dart';
import '../../../core/services/biometric_service.dart';

/// Button for biometric authentication
class BiometricAuthButton extends ConsumerStatefulWidget {
  const BiometricAuthButton({
    super.key,
    required this.onPressed,
    this.text = 'Use biometric authentication',
    this.icon,
  });

  final VoidCallback onPressed;
  final String text;
  final IconData? icon;

  @override
  ConsumerState<BiometricAuthButton> createState() => _BiometricAuthButtonState();
}

class _BiometricAuthButtonState extends ConsumerState<BiometricAuthButton> {
  bool _isAvailable = false;
  bool _isLoading = true;
  List<BiometricType> _availableBiometrics = [];

  @override
  void initState() {
    super.initState();
    _checkBiometricAvailability();
  }

  Future<void> _checkBiometricAvailability() async {
    final biometricService = ref.read(biometricServiceProvider);
    
    try {
      final isAvailable = await biometricService.isAvailable();
      final availableBiometrics = await biometricService.getAvailableBiometrics();
      
      if (mounted) {
        setState(() {
          _isAvailable = isAvailable && availableBiometrics.isNotEmpty;
          _availableBiometrics = availableBiometrics;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isAvailable = false;
          _isLoading = false;
        });
      }
    }
  }

  IconData _getBiometricIcon() {
    if (widget.icon != null) return widget.icon!;
    
    if (_availableBiometrics.contains(BiometricType.face)) {
      return Icons.face;
    } else if (_availableBiometrics.contains(BiometricType.fingerprint)) {
      return Icons.fingerprint;
    } else {
      return Icons.security;
    }
  }

  String _getBiometricText() {
    if (_availableBiometrics.contains(BiometricType.face)) {
      return widget.text.replaceAll('biometric', 'Face ID');
    } else if (_availableBiometrics.contains(BiometricType.fingerprint)) {
      return widget.text.replaceAll('biometric', 'fingerprint');
    } else {
      return widget.text;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const SizedBox(
        height: 48,
        child: Center(
          child: SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (!_isAvailable) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return OutlinedButton.icon(
      onPressed: widget.onPressed,
      icon: Icon(_getBiometricIcon()),
      label: Text(_getBiometricText()),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        side: BorderSide(
          color: theme.colorScheme.outline,
        ),
      ),
    );
  }
}
